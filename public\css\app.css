*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}
    :root {
        --primary: 255 0 107;
        --primary-slate: 255 59 142;
        --primary-light: 255 237 244;
        --client-font: 'Rubik', sans-serif;
        --admin-font: 'Public Sans', sans-serif;
    }
    
    a,
    button,
    label,
    span {
  display: inline-block; font-style: initial;
}
    
    input,
    select,
    textarea {
  background-color: transparent;
  font-size: 15px;
}
    
    input::-moz-placeholder, select::-moz-placeholder, textarea::-moz-placeholder {
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(160 163 189 / var(--tw-text-opacity, 1));
}
    
    input::placeholder,
    select::placeholder,
    textarea::placeholder {
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(160 163 189 / var(--tw-text-opacity, 1));
}
    
    input:focus-within,
    select:focus-within,
    textarea:focus-within {
  outline-width: 0px;
}

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
  --tw-text-opacity: 1;
  color: rgb(31 31 57 / var(--tw-text-opacity, 1)); font-style: initial;
}

    body {
  scroll-behavior: smooth;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  font-family: 'Rubik', sans-serif;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(110 113 145 / var(--tw-text-opacity, 1));
}
.container {
  width: 100%;
}
@media (min-width: 0px) {

  .container {
    max-width: 0px;
  }
}
@media (min-width: 640px) {

  .container {
    max-width: 640px;
  }
}
@media (min-width: 768px) {

  .container {
    max-width: 768px;
  }
}
@media (min-width: 1024px) {

  .container {
    max-width: 1024px;
  }
}
@media (min-width: 1280px) {

  .container {
    max-width: 1280px;
  }
}
@media (min-width: 1536px) {

  .container {
    max-width: 1536px;
  }
}
.container {
  margin-left: auto;
  margin-right: auto;
  max-width: 72rem;
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
@media (min-width: 640px) {

  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}
.row {
  margin: -0.75rem;
  display: flex;
  flex-wrap: wrap;
}
.col-12 {
  width: 100%;
  padding: 0.75rem;
}
.ql-editor h1 {
  margin-bottom: 1rem;
  font-size: 2.25rem;
  line-height: 2.5rem;
  font-weight: 700;
}
.ql-editor h2 {
  margin-bottom: 0.75rem;
  font-size: 1.875rem;
  line-height: 2.25rem;
  font-weight: 600;
}
.ql-editor h3 {
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 600;
}
.ql-editor h4 {
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 500;
}
.ql-editor h5 {
  margin-bottom: 0.25rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 500;
}
.ql-editor h6 {
  margin-bottom: 0.25rem;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 500;
}
.form-row {
  margin-left: -0.25rem;
  margin-right: -0.25rem;
  margin-top: -0.5rem;
  margin-bottom: -0.5rem;
  display: flex;
  flex-wrap: wrap;
}
.form-col-12 {
  width: 100%;
  padding-left: 0.25rem;
  padding-right: 0.25rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
/*===================================
              LAYOUT PART START
    ====================================*/
/* HEADER */
.db-header {
  position: fixed;
  top: 0px;
  left: 0px;
  z-index: 30;
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding: 1rem;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
@media (min-width: 1024px) {

  .db-header {
    gap: 9rem;
  }
}
/* SIDEBAR */
.db-sidebar {
  overflow-y: auto;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.db-sidebar:hover::-webkit-scrollbar-thumb {
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 107 / var(--tw-bg-opacity, 1));
}
.db-sidebar::-webkit-scrollbar {
  width: 3px;
  border-radius: 1rem;
  background-color: transparent;
}
.db-sidebar {
  position: fixed;
  top: 0px;
  z-index: 39;
  height: 100vh;
  width: 260px;
  overflow-y: auto;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding: 1rem;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
@media (min-width: 1024px) {

  .db-sidebar {
    top: 4rem;
    height: calc(100vh - 64px);
  }
}
.db-sidebar:where([dir="ltr"], [dir="ltr"] *) {
  left: 0px;
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  --tw-shadow: 0 0.125rem 0.375rem 0 rgb(161 172 184 / 12%);
  --tw-shadow-colored: 0 0.125rem 0.375rem 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
@media (min-width: 1024px) {

  .db-sidebar:where([dir="ltr"], [dir="ltr"] *) {
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
}
.db-sidebar:where([dir="rtl"], [dir="rtl"] *) {
  right: 0px;
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  --tw-shadow: 0 0.125rem -0.375rem 0 rgb(161 172 184 / 12%);
  --tw-shadow-colored: 0 0.125rem -0.375rem 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
@media (min-width: 1024px) {

  .db-sidebar:where([dir="rtl"], [dir="rtl"] *) {
    --tw-translate-x: -0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
}
.db-sidebar.active {
  --tw-shadow: 15px 0px 25px 0px rgba(0, 0, 0, 0.08);
  --tw-shadow-colored: 15px 0px 25px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
@media (min-width: 1024px) {

  .db-sidebar.active {
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
}
.db-sidebar.active:where([dir="ltr"], [dir="ltr"] *) {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@media (min-width: 1024px) {

  .db-sidebar.active:where([dir="ltr"], [dir="ltr"] *) {
    --tw-translate-x: -100%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
}
.db-sidebar.active:where([dir="rtl"], [dir="rtl"] *) {
  --tw-translate-x: -0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@media (min-width: 1024px) {

  .db-sidebar.active:where([dir="rtl"], [dir="rtl"] *) {
    --tw-translate-x: 100%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
}
.db-sidebar-header {
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (min-width: 1024px) {

  .db-sidebar-header {
    display: none;
  }
}
.db-sidebar-nav {
  margin-bottom: 1.5rem;
}
.db-sidebar-nav:last-child {
  margin-bottom: 0px;
}
.db-sidebar-nav-title {
  margin-bottom: 0.75rem;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 2px;
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.db-sidebar-nav-item.active .db-sidebar-nav-menu {
  background-color: rgb(255 0 107 / 0.05);
  --tw-text-opacity: 1;
  color: rgb(255 0 107 / var(--tw-text-opacity, 1));
}
.db-sidebar-nav-item.router-link-active .db-sidebar-nav-menu {
  background-color: rgb(255 0 107 / 0.05);
  --tw-text-opacity: 1;
  color: rgb(255 0 107 / var(--tw-text-opacity, 1));
}
.db-sidebar-nav-menu.router-link-active {
  background-color: rgb(255 0 107 / 0.05);
  --tw-text-opacity: 1;
  color: rgb(255 0 107 / var(--tw-text-opacity, 1));
}
.db-sidebar-nav-menu {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: flex-start;
  gap: 0.75rem;
  border-radius: 0.5rem;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  text-align: left;
  text-transform: capitalize;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.db-sidebar-nav-menu:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.db-sidebar-nav-menu i {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.db-sidebar-nav-menu span {
  flex: 1 1 auto;
  font-size: 1rem;
  line-height: 1.5rem;
}
.db-sidebar-nav-menu span:where([dir="ltr"], [dir="ltr"] *) {
  text-align: left;
}
.db-sidebar-nav-menu span:where([dir="rtl"], [dir="rtl"] *) {
  text-align: right;
}
.db-sidebar-nav-menu.downarrow::after {
  font-family: 'Font Awesome 6 Free';
  font-size: 10px;
  font-weight: 700;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  --tw-content: '\f054'; content: var(--tw-content); transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 300ms; transition-timing-function: linear;
}
.db-sidebar-nav-item.active .downarrow::after {
  content: var(--tw-content);
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.db-message-list .active {
  background-color: rgb(255 0 107 / 0.05) !important;
}
.db-message-list .active h4 {
  --tw-text-opacity: 1;
  color: rgb(255 0 107 / var(--tw-text-opacity, 1));
}
/*===================================
              LAYOUT PART END
    ====================================*/
/*===================================
            COMPONENTS PART START
    ====================================*/
.ff-header .container {
  height: 118px;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
@media (min-width: 1024px) {

  .ff-header .container {
    height: 74px;
    padding-top: 0px;
    padding-bottom: 0px;
  }
}
.ff-header.active {
  position: fixed;
  top: 0px;
  left: 0px;
  z-index: 60;
  width: 100%;
}
#cart.active {
  visibility: visible;
  opacity: 1;
}
#cart.active > div:where([dir="ltr"], [dir="ltr"] *) {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
#cart.active > div:where([dir="rtl"], [dir="rtl"] *) {
  --tw-translate-x: -0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.fd-backdrop.active {
  visibility: visible;
  opacity: 1;
}
.cart-switch:checked + label {
  --tw-bg-opacity: 1;
  background-color: rgb(0 139 186 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.size-tabs .active {
  --tw-border-opacity: 1;
  border-color: rgb(255 0 107 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(220 234 255 / var(--tw-bg-opacity, 1));
}
.size-tabs .active .custom-radio-field:checked + .custom-radio-span {
  --tw-border-opacity: 1;
  border-color: rgb(255 0 107 / var(--tw-border-opacity, 1));
}
.extra .custom-checkbox-field {
  z-index: 0;
}
.extra.active {
  --tw-border-opacity: 1 !important;
  border-color: rgb(255 0 107 / var(--tw-border-opacity, 1)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(220 234 255 / var(--tw-bg-opacity, 1)) !important;
}
.extra.active .custom-checkbox-field:checked + .custom-checkbox-icon {
  --tw-border-opacity: 1;
  border-color: rgb(255 0 107 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 107 / var(--tw-bg-opacity, 1));
}
.addon.active {
  --tw-border-opacity: 1 !important;
  border-color: rgb(255 0 107 / var(--tw-border-opacity, 1)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(220 234 255 / var(--tw-bg-opacity, 1)) !important;
}
.branch-navs .active {
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 107 / var(--tw-bg-opacity, 1));
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.branch-navs .active:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 107 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.active-group .active {
  border-color: rgb(255 0 107 / 0.5) !important;
  background-color: rgb(255 0 107 / 0.05) !important;
}
.active-group .active .fa-check {
  --tw-border-opacity: 1 !important;
  border-color: rgb(255 0 107 / var(--tw-border-opacity, 1)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 0 107 / var(--tw-bg-opacity, 1)) !important;
}
.active-group .active input:checked + .custom-radio-span {
  border-width: 3px !important;
  --tw-border-opacity: 1 !important;
  border-color: rgb(255 0 107 / var(--tw-border-opacity, 1)) !important;
}
.paper-link svg {
  stroke: #6E7191;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.paper-link span {
  --tw-text-opacity: 1;
  color: rgb(31 31 57 / var(--tw-text-opacity, 1));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.paper-link:hover svg,
    .paper-link:hover span {
  stroke: rgb(255 0 107 / 1);
  --tw-text-opacity: 1;
  color: rgb(255 0 107 / var(--tw-text-opacity, 1));
}
.address-btn:hover svg,
    .address-btn:hover span {
  fill: #fff;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.cookie-paper {
  visibility: hidden;
  --tw-translate-y: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  opacity: 0;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 500ms;
  transition-timing-function: linear;
}
.cookie-paper.active {
  visibility: visible;
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  opacity: 1;
}
.add-btn:hover svg {
  fill: #fff;
}
.add-btn:hover span {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.info-btn:hover svg {
  fill: rgb(255 0 107 / 1);
}
#profile.active {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.profile-paper {
  visibility: hidden;
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  opacity: 0;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.profile-paper.active {
  visibility: visible;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  opacity: 1;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.veg-navs .veg-active {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  --tw-shadow: 0px 8px 16px rgba(23, 31, 70, 0.08);
  --tw-shadow-colored: 0px 8px 16px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.veg-navs .veg-active i {
  opacity: 1;
}
.veg-navs .veg-active i:where([dir="ltr"], [dir="ltr"] *) {
  margin-left: 0px;
}
.veg-navs .veg-active i:where([dir="rtl"], [dir="rtl"] *) {
  margin-right: 0px;
}
.menu-slides .menu-category-active {
  --tw-border-opacity: 1;
  border-color: rgb(255 0 107 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(220 234 255 / var(--tw-bg-opacity, 1));
}
.ff-header.landing-header.active {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  --tw-shadow: 0px 6px 32px 0px rgba(0, 0, 0, 0.04);
  --tw-shadow-colored: 0px 6px 32px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
/* PAYMENT CARD */
.payment-fieldset {
  margin-bottom: 3rem;
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  justify-content: center;
  gap: 1.5rem;
}
@media (min-width: 768px) {

  .payment-fieldset {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}
.payment-label {
  position: relative;
  display: flex;
  width: 100%;
  cursor: pointer;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  text-align: center;
  --tw-shadow: 0px 6px 32px 0px rgba(0, 0, 0, 0.04);
  --tw-shadow-colored: 0px 6px 32px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}
.payment-label.active {
  --tw-border-opacity: 1;
  border-color: rgb(255 0 107 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(220 234 255 / var(--tw-bg-opacity, 1));
}
.payment-label input {
  position: absolute;
  top: 0.625rem;
  left: 0.625rem;
  accent-color: rgb(255 0 107 / 1);
}
.payment-label img {
  height: 2rem;
}
.payment-label span {
  font-size: 0.875rem;
  line-height: 1.25rem;
  text-transform: capitalize;
  --tw-text-opacity: 1;
  color: rgb(31 31 57 / var(--tw-text-opacity, 1));
}
/* PRODUCT CARD GRID */
.product-card-grid {
  position: relative;
  display: flex;
  flex-direction: column;
  border-radius: 1rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(239 240 246 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.product-card-grid:hover {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.product-card-grid-image {
  width: 100%;
  border-top-left-radius: 1rem;
  border-top-right-radius: 1rem;
}
.product-card-grid-content-group {
  display: flex;
  height: 100%;
  flex-direction: column;
  justify-content: space-between;
  border-bottom-right-radius: 1rem;
  border-bottom-left-radius: 1rem;
  padding-top: 1rem;
  padding-bottom: 1rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.product-card-grid-header-group {
  margin-bottom: 0.5rem;
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}
.product-card-grid-title {
  width: -moz-fit-content;
  width: fit-content;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 600;
  text-transform: capitalize;
}
.product-card-grid-describe {
  margin-bottom: 0.75rem;
  text-overflow: ellipsis;
  font-size: 0.75rem;
  line-height: 1rem;
}
.product-card-grid-footer-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
}
.product-card-grid-price-group {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  -moz-column-gap: 0.5rem;
       column-gap: 0.5rem;
}
.product-card-grid-price-previous {
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
}
.product-card-grid-price-current {
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 500;
}
.product-card-grid-cart-btn {
  display: flex;
  height: 1.5rem;
  align-items: center;
  gap: 0.375rem;
  border-radius: 1.5rem;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  text-transform: capitalize;
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.product-card-grid-cart-btn:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 107 / var(--tw-bg-opacity, 1));
}
/* PRODUCT CARD LIST */
.product-card-list {
  position: relative;
  display: flex;
  align-items: center;
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(239 240 246 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.product-card-list:hover {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.product-card-list-image {
  height: 100%;
  width: 7rem;
  flex-shrink: 0;
  -o-object-fit: cover;
     object-fit: cover;
}
.product-card-list-image:where([dir="ltr"], [dir="ltr"] *) {
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.product-card-list-image:where([dir="rtl"], [dir="rtl"] *) {
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}
.product-card-list-content-group {
  display: flex;
  height: 100%;
  width: 100%;
  flex-direction: column;
  justify-content: space-between;
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
  padding: 0.75rem;
}
.product-card-list-header-group {
  margin-bottom: 0.5rem;
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}
.product-card-list-title {
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 600;
  text-transform: capitalize;
}
.product-card-list-describe {
  margin-bottom: 0.75rem;
  text-overflow: ellipsis;
  font-size: 0.75rem;
  line-height: 1rem;
}
.product-card-list-footer-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
}
.product-card-list-price-group {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  -moz-column-gap: 0.5rem;
       column-gap: 0.5rem;
}
.product-card-list-price-current {
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 500;
}
.product-card-list-cart-btn {
  display: flex;
  height: 1.5rem;
  align-items: center;
  gap: 0.375rem;
  border-radius: 1.5rem;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  text-transform: capitalize;
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.product-card-list-cart-btn:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 107 / var(--tw-bg-opacity, 1));
}
/* CHAT MESSAGE */
.chat-list {
  display: flex;
  flex-direction: column-reverse;
  gap: 1.25rem;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 1rem;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.chat-list.frontend {
  height: calc(100vh - 260px);
}
@media (min-width: 640px) {

  .chat-list.frontend {
    height: calc(100vh - 250px);
  }
}
.chat-list.backend {
  height: 400px;
}
@media (min-width: 640px) {

  .chat-list.backend {
    height: calc(100vh - 315px);
  }
}
.chat-list.frontend.change {
  height: calc(100vh - 320px);
}
@media (min-width: 640px) {

  .chat-list.frontend.change {
    height: calc(100vh - 300px);
  }
}
.chat-list.backend.change {
  height: 400px;
}
@media (min-width: 640px) {

  .chat-list.backend.change {
    height: calc(100vh - 380px);
  }
}
.chat-item {
  display: flex;
  align-items: flex-end;
  gap: 0.75rem;
}
.chat-avatar {
  margin-bottom: 1.25rem;
  height: 1.75rem;
  width: 1.75rem;
  flex-shrink: 0;
  border-radius: 9999px;
}
.chat-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}
.chat-group-text {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}
.chat-text {
  width: -moz-fit-content;
  width: fit-content;
  max-width: 24rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 1rem;
  padding-right: 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(31 31 57 / var(--tw-text-opacity, 1));
}
.chat-group-meta {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  gap: 0.25rem;
}
.chat-meta {
  font-size: 0.75rem;
  line-height: 1rem;
}
.chat-user {
  flex-direction: row-reverse;
}
.chat-admin .chat-text {
  border-radius: 1rem;
  --tw-bg-opacity: 1;
  background-color: rgb(239 240 246 / var(--tw-bg-opacity, 1));
}
.chat-admin .chat-text:nth-child(odd):where([dir="ltr"], [dir="ltr"] *) {
  border-bottom-left-radius: 0.25rem;
}
.chat-admin .chat-text:nth-child(even):where([dir="ltr"], [dir="ltr"] *) {
  border-top-left-radius: 0.25rem;
}
.chat-admin .chat-text:nth-child(odd):where([dir="rtl"], [dir="rtl"] *) {
  border-bottom-right-radius: 0.25rem;
}
.chat-admin .chat-text:nth-child(even):where([dir="rtl"], [dir="rtl"] *) {
  border-top-right-radius: 0.25rem;
}
.chat-user .chat-text {
  border-radius: 1rem;
  --tw-bg-opacity: 1;
  background-color: rgb(220 234 255 / var(--tw-bg-opacity, 1));
}
.chat-user .chat-text:nth-child(odd):where([dir="ltr"], [dir="ltr"] *) {
  border-bottom-right-radius: 0.25rem;
}
.chat-user .chat-text:nth-child(even):where([dir="ltr"], [dir="ltr"] *) {
  border-top-right-radius: 0.25rem;
}
.chat-user .chat-text:nth-child(odd):where([dir="rtl"], [dir="rtl"] *) {
  border-bottom-left-radius: 0.25rem;
}
.chat-user .chat-text:nth-child(even):where([dir="rtl"], [dir="rtl"] *) {
  border-top-left-radius: 0.25rem;
}
.chat-user .chat-group-text {
  align-items: flex-end;
}
.chat-user .chat-group-meta {
  flex-direction: row-reverse;
}
.chat-list::-webkit-scrollbar {
  width: 0.5rem;
  background-color: transparent;
}
.chat-list::-webkit-scrollbar-thumb {
  border-radius: 0.75rem;
  border-width: 2px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}
.chat-footer {
  display: flex;
  width: 100%;
  align-items: center;
  gap: 1.25rem;
  padding: 1rem;
}
.chat-footer-file-label {
  position: relative;
  isolation: isolate;
  height: -moz-fit-content;
  height: fit-content;
  width: -moz-fit-content;
  width: fit-content;
  flex-shrink: 0;
  cursor: pointer;
}
.chat-footer-file-input {
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: -10;
  height: 100%;
  width: 100%;
  opacity: 0;
}
.chat-footer-data {
  width: 100%;
  overflow: hidden;
  border-radius: 1.5rem;
  --tw-bg-opacity: 1;
  background-color: rgb(247 247 252 / var(--tw-bg-opacity, 1));
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
}
.chat-footer-data-list {
  display: flex;
  gap: 0.75rem;
  overflow-x: auto;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.chat-footer-data-list::-webkit-scrollbar {
  height: 0.5rem;
  border-radius: 0.75rem;
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.chat-footer-data-list::-webkit-scrollbar-thumb {
  border-radius: 0.75rem;
  border-width: 2px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}
.chat-footer-data-input {
  height: 100%;
  width: 100%;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.chat-footer-sent {
  flex-shrink: 0;
}
/* THIN SCROLL BAR */
.thin-scrolling {
  overflow-y: auto;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.thin-scrolling:hover::-webkit-scrollbar-thumb {
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 107 / var(--tw-bg-opacity, 1));
}
.thin-scrolling::-webkit-scrollbar {
  width: 3px;
  border-radius: 1rem;
  background-color: transparent;
}
/* OTHER */
.db-image {
  width: 100%;
  border-radius: 10px;
  border-width: 5px;
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.db-light-text {
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 300;
  letter-spacing: 0.025em;
}
/* ORDER STATUS */
.db-order-status.check::before {
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 107 / var(--tw-bg-opacity, 1));
  text-align: center;
  font-family: 'Font Awesome 6 Free';
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  --tw-content: '\f00c';
  content: var(--tw-content);
}
.db-order-status.active::before {
  content: var(--tw-content);
  border-width: 5px;
}
/* COMMON CLASS */
.invalid {
  --tw-border-opacity: 1 !important;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1)) !important;
}
.invalid::before {
  content: var(--tw-content) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(252 165 165 / var(--tw-bg-opacity, 1)) !important;
}
/* MAIN CONTENT */
.db-main {
  height: 100vh;
  overflow: auto;
  --tw-bg-opacity: 1;
  background-color: rgb(247 247 252 / var(--tw-bg-opacity, 1));
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-bottom: 1rem;
  padding-top: 80px;
  font-family: 'Public Sans', sans-serif;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
@media (min-width: 640px) {

  .db-main {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}
@media (min-width: 1024px) {

  .db-main {
    padding-top: 84px;
    padding-bottom: 1.25rem;
  }
}
@media (min-width: 0px) and (max-width: 767px) {

  .db-main {
    padding-top: 170px;
  }
}
@media (min-width: 1024px) {

  .db-main:where([dir="ltr"], [dir="ltr"] *) {
    padding-left: 280px;
    padding-right: 1.25rem;
  }

  .db-main:where([dir="rtl"], [dir="rtl"] *) {
    padding-right: 280px;
    padding-left: 1.25rem;
  }

  .db-main.expand:where([dir="ltr"], [dir="ltr"] *) {
    padding-left: 1.25rem;
  }

  .db-main.expand:where([dir="rtl"], [dir="rtl"] *) {
    padding-right: 1.25rem;
  }
}
/* BACKLDROP */
.backdrop {
  visibility: hidden;
  position: fixed;
  top: 0px;
  left: 0px;
  z-index: 40;
  height: 100%;
  width: 100%;
  background-color: rgb(0 0 0 / 0.6);
  opacity: 0;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.backdrop.active {
  visibility: visible;
  opacity: 1;
}
/* BUTTON */
.db-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border-radius: 0.375rem;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: 1rem;
  padding-right: 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 400;
  text-transform: capitalize;
  --tw-shadow: 0 2px 6px 0 rgb(67 89 113 / 12%);
  --tw-shadow-colored: 0 2px 6px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.xmark-btn {
  height: 1.5rem;
  width: 1.5rem;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
  text-align: center;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.xmark-btn:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 0 107 / var(--tw-text-opacity, 1));
}
.db-btn-outline {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.375rem;
  border-radius: 0.375rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 400;
  text-transform: capitalize;
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.db-btn-outline:hover {
  border-color: rgb(255 0 107 / 0.2);
  background-color: rgb(255 0 107 / 0.05);
  --tw-text-opacity: 1;
  color: rgb(255 0 107 / var(--tw-text-opacity, 1));
}
.db-btn-outline i {
  font-size: 0.75rem;
  line-height: 1rem;
}
.db-btn-outline.primary {
  --tw-border-opacity: 1;
  border-color: rgb(255 0 107 / var(--tw-border-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 0 107 / var(--tw-text-opacity, 1));
}
.db-btn-outline.primary:hover {
  background-color: rgb(255 0 107 / 0.1);
}
.db-btn-outline.warning {
  --tw-border-opacity: 1;
  border-color: rgb(246 166 9 / var(--tw-border-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(246 166 9 / var(--tw-text-opacity, 1));
}
.db-btn-outline.warning:hover {
  background-color: rgb(246 166 9 / 0.1);
}
.db-btn-outline.success {
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}
.db-btn-outline.success:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}
.db-btn-outline.danger {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.db-btn-outline.danger:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}
.db-btn-outline.info {
  --tw-border-opacity: 1;
  border-color: rgb(6 182 212 / var(--tw-border-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(8 145 178 / var(--tw-text-opacity, 1));
}
.db-btn-outline.info:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(236 254 255 / var(--tw-bg-opacity, 1));
}
.db-btn-outline.sm {
  gap: 0.25rem !important;
  border-radius: 0.25rem !important;
  padding-top: 0.25rem !important;
  padding-bottom: 0.25rem !important;
  padding-left: 0.5rem !important;
  padding-right: 0.5rem !important;
  font-size: 0.75rem !important;
  line-height: 1rem !important;
}
.db-btn-outline.sm i {
  font-size: 10px !important;
}
.db-btn-fill.primary {
  background-color: rgb(255 0 107 / 0.1);
  --tw-text-opacity: 1;
  color: rgb(255 0 107 / var(--tw-text-opacity, 1));
}
.db-btn-fill.primary:hover {
  background-color: rgb(255 0 107 / 0.2);
}
.db-btn-fill.sm {
  gap: 0.25rem !important;
  border-radius: 0.25rem !important;
  padding-top: 0.25rem !important;
  padding-bottom: 0.25rem !important;
  padding-left: 0.5rem !important;
  padding-right: 0.5rem !important;
  font-size: 0.75rem !important;
  line-height: 1rem !important;
}
.db-btn-fill.sm i {
  font-size: 10px !important;
}
/* CARD */
.db-card {
  border-radius: 0.25rem;
  border-style: none;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  --tw-shadow: 0 2px 6px 0 rgb(67 89 113 / 12%);
  --tw-shadow-colored: 0 2px 6px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.db-card-body {
  padding: 1.25rem;
}
.db-card-header {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  gap: 1.25rem;
  border-bottom-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  padding: 1.25rem;
}
@media (min-width: 640px) {

  .db-card-header {
    flex-direction: row;
    justify-content: space-between;
  }
}
.db-card-title {
  font-family: 'Public Sans', sans-serif;
  font-size: 18px;
  font-weight: 500;
  text-transform: capitalize;
  --tw-text-opacity: 1;
  color: rgb(110 113 145 / var(--tw-text-opacity, 1));
}
.db-card-filter {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  gap: 0.625rem;
}
@media (min-width: 640px) {

  .db-card-filter {
    align-items: flex-start;
  }
}
.db-card-filter-btn {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0.375rem;
  border-radius: 0.375rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(255 0 107 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding-left: 0.625rem;
  padding-right: 0.625rem;
  padding-top: 0.5rem;
  padding-bottom: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  text-transform: capitalize;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(110 113 145 / var(--tw-text-opacity, 1));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.db-card-filter-btn:hover {
  border-color: rgb(255 0 107 / 0.1);
  background-color: rgb(255 0 107 / 0.05);
  --tw-text-opacity: 1;
  color: rgb(255 0 107 / var(--tw-text-opacity, 1));
}
.db-card-filter-btn i {
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(255 0 107 / var(--tw-text-opacity, 1));
}
.db-card-filter-btn span {
  --tw-text-opacity: 1;
  color: rgb(255 0 107 / var(--tw-text-opacity, 1));
}
.db-card-filter-btn span:where([dir="ltr"], [dir="ltr"] *) {
  padding-right: 0.25rem;
}
.db-card-filter-btn span:where([dir="rtl"], [dir="rtl"] *) {
  padding-left: 0.25rem;
}
.db-card-filter .db-field-down-arrow {
  --tw-text-opacity: 1;
  color: rgb(255 0 107 / var(--tw-text-opacity, 1));
}
.db-card-filter-dropdown-list {
  position: absolute;
  top: 2.5rem;
  right: 0px;
  z-index: 20;
  border-radius: 0.375rem;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding: 0.5rem;
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.db-card-filter-dropdown-menu {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0.625rem;
  white-space: nowrap;
  border-radius: 0.375rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  text-transform: capitalize;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.db-card-filter-dropdown-menu:hover {
  background-color: rgb(255 0 107 / 0.05);
  --tw-text-opacity: 1;
  color: rgb(255 0 107 / var(--tw-text-opacity, 1));
}
.db-card-filter-dropdown-menu i {
  width: 1rem;
  flex-shrink: 0;
}
.dropdown-btn i ,.table-filter-btn i,.custom-dropdown .dropdown-btn i{
        transition: transform 0.3s ease;
    }
.dropdown-btn.rotated i:last-child ,.table-filter-btn.rotated i:last-child,.custom-dropdown .dropdown-btn.rotated i{
        transform: rotate(180deg);
    }
/* BREADCRUMB */
.db-breadcrumb {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  row-gap: 0.5rem;
  -moz-column-gap: 1.25rem;
       column-gap: 1.25rem;
  padding-top: 14px;
}
@media (min-width: 640px) {

  .db-breadcrumb {
    flex-direction: row;
    justify-content: space-between;
  }
}
.db-breadcrumb-list {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}
@media (min-width: 640px) {

  .db-breadcrumb-list {
    justify-content: flex-end;
  }
}
.db-breadcrumb-item {
  font-size: 22px;
  text-transform: capitalize;
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity, 1));
}
.db-breadcrumb-link {
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity, 1));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.db-breadcrumb-link::after {
  padding-left: 0.375rem;
  padding-right: 0.375rem;
  --tw-text-opacity: 1;
  color: rgb(110 113 145 / var(--tw-text-opacity, 1));
  --tw-content: '/';
  content: var(--tw-content);
}
.db-breadcrumb-link:hover {
  --tw-text-opacity: 1;
  color: rgb(255 0 107 / var(--tw-text-opacity, 1));
}
/* TABS */
.db-tab-btn {
  display: flex;
  width: 100%;
  align-items: center;
  gap: 0.75rem;
  border-radius: 0.25rem;
  border-bottom-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(241 245 249 / var(--tw-border-opacity, 1));
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  text-align: left;
  font-size: 15px;
  text-transform: capitalize;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.db-tab-btn:last-child {
  border-style: none;
}
.db-tab-btn:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));
}
.db-tab-btn i {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.db-tab-btn.active {
  background-color: rgb(255 0 107 / 0.05) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(255 0 107 / var(--tw-text-opacity, 1)) !important;
}
.db-tab-div {
  display: none;
}
.db-tab-div.active {
  display: block;
}
.db-tab-sub-div {
  display: none;
}
.db-tab-sub-div.active {
  display: block;
}
.db-tab-sub-btn.active {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 0 107 / var(--tw-bg-opacity, 1)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}
.db-tabBtn {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border-radius: 0.25rem;
  border-width: 1px;
  border-color: transparent;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  text-transform: capitalize;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.db-tabBtn:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 0 107 / var(--tw-text-opacity, 1));
}
@media (min-width: 640px) {

  .db-tabBtn {
    border-bottom-right-radius: 0px;
    border-bottom-left-radius: 0px;
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
    border-bottom-color: rgb(255 0 107 / 0.5);
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }
}
.db-tabBtn.active {
  border-color: rgb(255 0 107 / 0.5);
  background-color: rgb(255 0 107 / 0.05);
  --tw-text-opacity: 1;
  color: rgb(255 0 107 / var(--tw-text-opacity, 1));
}
@media (min-width: 640px) {

  .db-tabBtn.active {
    --tw-border-opacity: 1;
    border-bottom-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  }
}
.db-tabDiv {
  display: none;
  border-radius: 0.25rem;
  border-width: 1px;
  border-color: rgb(255 0 107 / 0.5);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 1rem;
  padding-right: 1rem;
  font-size: 0.875rem;
  line-height: 1.5rem;
  --tw-shadow: 0 2px 6px 0 rgb(67 89 113 / 12%);
  --tw-shadow-colored: 0 2px 6px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
@media (min-width: 640px) {

  .db-tabDiv {
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
    border-left-width: 1px;
    border-right-width: 1px;
    border-bottom-width: 1px;
    --tw-border-opacity: 1;
    border-top-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
    padding-top: 1rem;
    padding-bottom: 1rem;
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }
}
.db-tabDiv.active {
  display: block;
}
.db-tabBtn span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.data-tab {
  display: none;
}
.data-tab.active {
  display: block !important;
}
.profile-tabBtn.active {
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 107 / var(--tw-bg-opacity, 1));
  stroke: #fff;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  --tw-shadow: 0px 6px 10px rgba(255, 0, 107, 0.24);
  --tw-shadow-colored: 0px 6px 10px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.profile-tabDiv {
  display: none;
}
.profile-tabDiv.active {
  display: block;
}
/* DROPDOWN */
.dropdown-group {
  position: relative;
  line-height: 0px;
}
.dropdown-btn {
  cursor: pointer;
}
.dropdown-list {
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.dropdown-list.active {
  transform-origin: top;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
/* FIELDS */
.db-field-title {
  margin-bottom: 0.5rem;
  display: block;
  font-family: 'Public Sans', sans-serif;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 500;
  text-transform: uppercase;
  --tw-text-opacity: 1;
  color: rgb(86 106 127 / var(--tw-text-opacity, 1));
}
.db-field-title.required::after {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
  --tw-content: '*';
  content: var(--tw-content);
}
.db-field-title.required:where([dir="ltr"], [dir="ltr"] *)::after {
  content: var(--tw-content);
  margin-left: 0.25rem;
}
.db-field-title.required:where([dir="rtl"], [dir="rtl"] *)::after {
  content: var(--tw-content);
  margin-right: 0.25rem;
}
.db-field-control {
  display: block;
  height: 2.5rem;
  width: 100%;
  border-radius: 0.375rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-bottom: 2px;
  font-family: 'Public Sans', sans-serif;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.db-field-control:focus-within {
  border-color: rgb(255 0 107 / 0.2);
}
.db-multiple-field {
  display: flex;
  height: 2.5rem;
  width: 100%;
  align-items: center;
  border-radius: 0.375rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.db-multiple-field:focus-within {
  border-color: rgb(255 0 107 / 0.2);
}
.db-multiple-field input {
  height: 100%;
  width: 50%;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-bottom: 2px;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.db-multiple-field input:first-child {
  border-style: none;
}
.db-multiple-field input:where([dir="ltr"], [dir="ltr"] *) {
  border-left-width: 1px;
}
.db-multiple-field input:where([dir="rtl"], [dir="rtl"] *) {
  border-right-width: 1px;
}
.db-multiple-field button {
  height: 2rem;
  border-radius: 0.375rem;
  background-color: rgb(255 0 107 / 0.1);
  padding-left: 1rem;
  padding-right: 1rem;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-text-opacity: 1;
  color: rgb(255 0 107 / var(--tw-text-opacity, 1));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.db-multiple-field button:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 107 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.db-multiple-field button:where([dir="ltr"], [dir="ltr"] *) {
  margin-right: 3px;
}
.db-multiple-field button:where([dir="rtl"], [dir="rtl"] *) {
  margin-left: 3px;
}
.db-field-alert {
  margin-left: 0.25rem;
  font-family: 'Public Sans', sans-serif;
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.db-field-radio-group {
  display: flex;
  align-items: center;
  gap: 1.25rem;
  padding-top: 0.5rem;
}
.db-field-radio {
  display: flex;
  width: -moz-fit-content;
  width: fit-content;
  cursor: pointer;
  align-items: center;
  padding-top: 0.25rem;
}
.db-field-checkbox {
  display: flex;
  width: -moz-fit-content;
  width: fit-content;
  cursor: pointer;
  align-items: center;
  padding-top: 0.25rem;
}
.db-field-label {
  cursor: pointer;
  font-family: 'Public Sans', sans-serif;
  font-size: 15px;
  text-transform: capitalize;
  line-height: 1rem;
}
.db-field-label:where([dir="ltr"], [dir="ltr"] *) {
  padding-left: 0.5rem;
}
.db-field-label:where([dir="rtl"], [dir="rtl"] *) {
  padding-right: 0.5rem;
}
textarea.db-field-control {
  height: 9rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.db-field-control[type=file] {
  position: relative;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.db-field-control[type=file]::before {
  position: absolute;
  top: 0px;
  z-index: 10;
  height: 100%;
  width: 1px;
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  --tw-content: ''; content: var(--tw-content); transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 300ms; transition-timing-function: linear;
}
.db-field-control[type=file]:focus-within::before {
  content: var(--tw-content);
  background-color: rgb(255 0 107 / 0.2);
}
.db-field-control[type=file]:where([dir="ltr"], [dir="ltr"] *) {
  padding-left: 0px;
}
.db-field-control[type=file]:where([dir="ltr"], [dir="ltr"] *)::before {
  content: var(--tw-content);
  left: 107px;
}
.db-field-control[type=file]:where([dir="rtl"], [dir="rtl"] *) {
  padding-right: 0px;
}
.db-field-control[type=file]:where([dir="rtl"], [dir="rtl"] *)::before {
  content: var(--tw-content);
  right: 113px;
}
.db-field-control[type=file]::-webkit-file-upload-button,
    .db-field-control[type=file]::file-selector-button {
  height: 2.5rem;
  cursor: pointer;
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
  border-style: none;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding-left: 1rem;
  padding-bottom: 0.25rem;
  padding-right: 1.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(110 113 145 / var(--tw-text-opacity, 1));
  -webkit-transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  -webkit-transition-property: all;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.checkbox:checked {
  border-style: none;
}
.checkbox:checked + .check-icon {
  display: flex;
}
.checkbox:checked + .checkmark {
  --tw-border-opacity: 1;
  border-color: rgb(255 0 107 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 107 / var(--tw-bg-opacity, 1));
}
input:checked + div {
  --tw-border-opacity: 1;
  border-color: rgb(255 0 107 / var(--tw-border-opacity, 1));
}
input:checked + div svg {
  display: block;
}
.custom-switch {
  display: inline-flex;
  cursor: pointer;
  align-items: center;
  justify-content: center;
}
.custom-switch input {
  position: relative;
  height: 0.75rem;
  width: 1.25rem;
  cursor: pointer;
}
.custom-switch input::before {
  position: absolute;
  inset: 0px;
  display: inline-block;
  height: 100%;
  width: 100%;
  border-radius: 1.5rem;
  --tw-bg-opacity: 1;
  background-color: rgb(110 113 145 / var(--tw-bg-opacity, 1));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  --tw-content: '';
  content: var(--tw-content);
}
.custom-switch input::after {
  position: absolute;
  top: 50%;
  left: 30%;
  height: 0.375rem;
  width: 0.375rem;
  --tw-translate-y: -50%;
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  --tw-content: '';
  content: var(--tw-content);
}
.custom-switch input:checked::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 107 / var(--tw-bg-opacity, 1));
}
.custom-switch input:checked::after {
  content: var(--tw-content);
  left: 70%;
}
.custom-switch label {
  cursor: pointer;
  font-size: 0.875rem;
  line-height: 1.25rem;
  text-transform: uppercase;
  --tw-text-opacity: 1;
  color: rgb(110 113 145 / var(--tw-text-opacity, 1));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}
.custom-switch label:where([dir="ltr"], [dir="ltr"] *) {
  padding-left: 0.5rem;
}
.custom-switch label:where([dir="rtl"], [dir="rtl"] *) {
  padding-right: 0.5rem;
}
.custom-switch input:checked + label {
  --tw-text-opacity: 1;
  color: rgb(255 0 107 / var(--tw-text-opacity, 1));
}
.custom-checkbox {
  position: relative;
  height: 1rem;
  width: 1rem;
  cursor: pointer;
}
.custom-checkbox-field {
  position: absolute;
  z-index: 10;
  height: 100%;
  width: 100%;
  cursor: pointer;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  opacity: 0;
}
.custom-checkbox-icon {
  position: absolute;
  top: 0px;
  left: 0px;
  height: 100%;
  width: 100%;
  cursor: pointer;
  border-radius: 0.25rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(217 222 227 / var(--tw-border-opacity, 1));
  text-align: center;
  font-size: 10px;
  line-height: 14px;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.custom-checkbox-field:checked + .custom-checkbox-icon {
  --tw-border-opacity: 1;
  border-color: rgb(255 0 107 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 107 / var(--tw-bg-opacity, 1));
  --tw-shadow: 0 2px 4px 0 rgb(105 108 255 / 40%);
  --tw-shadow-colored: 0 2px 4px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.custom-radio {
  position: relative;
  height: 1rem;
  width: 1rem;
  cursor: pointer;
}
.custom-radio-field {
  position: absolute;
  z-index: 10;
  height: 100%;
  width: 100%;
  cursor: pointer;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  opacity: 0;
}
.custom-radio-span {
  position: absolute;
  top: 0px;
  left: 0px;
  height: 100%;
  width: 100%;
  cursor: pointer;
  border-radius: 9999px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(217 222 227 / var(--tw-border-opacity, 1));
  text-align: center;
}
.custom-radio-field:checked + .custom-radio-span {
  border-width: 5px;
  --tw-border-opacity: 1;
  border-color: rgb(255 0 107 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  --tw-shadow: 0 2px 4px 0 rgb(105 108 255 / 40%);
  --tw-shadow-colored: 0 2px 4px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.custom-radio.sm {
  height: 0.75rem;
  width: 0.75rem;
}
.custom-radio.sm .custom-radio-field:checked + .custom-radio-span {
  border-width: 3px;
}
/* TABLE */
.db-table-responsive {
  width: 100%;
  overflow: auto;
}
.db-table {
  width: 100%;
  table-layout: auto;
  white-space: nowrap;
  text-align: left;
}
.db-table.stripe .db-table-body-tr:nth-child(odd) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1)) !important;
}
.db-table-head {
  border-bottom-width: 2px;
  border-top-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.db-table-head:where([dir="ltr"], [dir="ltr"] *) {
  text-align: left;
}
.db-table-head:where([dir="rtl"], [dir="rtl"] *) {
  text-align: right;
}
.db-table-body:where([dir="ltr"], [dir="ltr"] *) {
  text-align: left;
}
.db-table-body:where([dir="rtl"], [dir="rtl"] *) {
  text-align: right;
}
.db-table-head-th {
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  font-family: 'Public Sans', sans-serif !important;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  --tw-text-opacity: 1;
  color: rgb(86 106 127 / var(--tw-text-opacity, 1));
}
.db-table-body-tr {
  border-bottom-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  font-family: 'Public Sans', sans-serif;
}
.db-table-body-tr:last-child {
  border-style: none;
}
.db-table-body-td {
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  font-family: 'Public Sans', sans-serif;
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.db-table-badge {
  border-radius: 0.375rem;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  font-family: 'Public Sans', sans-serif;
  text-transform: capitalize;
}
.db-table-action {
  position: relative;
}
.db-table-action i {
  margin: 0.125rem;
  border-radius: 0.25rem;
  padding: 0.5rem;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.db-table-action.view i {
  background-color: rgb(255 0 107 / 0.1);
  --tw-text-opacity: 1;
  color: rgb(255 0 107 / var(--tw-text-opacity, 1));
}
.db-table-action.view i:hover {
  background-color: rgb(255 0 107 / 0.2);
}
.db-table-action.edit i {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}
.db-table-action.edit i:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(187 247 208 / var(--tw-bg-opacity, 1));
}
.db-table-action.delete i {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.db-table-action.delete i:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));
}
.table-filter-div {
  height: 0px;
  overflow: hidden;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 500ms;
  transition-timing-function: linear;
}
/* TOOLTIP */
.db-tooltip {
  visibility: hidden;
  position: absolute;
  left: 50%;
  top: -1.25rem;
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  white-space: nowrap;
  border-radius: 0.25rem;
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
  padding-left: 7px;
  padding-right: 7px;
  padding-top: 1px;
  padding-bottom: 2px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  opacity: 0;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.db-tooltip::after {
  position: absolute;
  bottom: -3px;
  left: 50%;
  --tw-translate-x: -50%;
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  border-radius: 0.125rem;
  border-width: 4px;
  border-top-width: 4px;
  border-style: solid;
  border-top-color: transparent;
  border-left-color: transparent;
  border-right-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
  --tw-border-opacity: 1;
  border-bottom-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
  --tw-content: '';
  content: var(--tw-content);
}
.db-table-action:hover .db-tooltip {
  visibility: visible;
  top: -100%;
  opacity: 1;
}
/* MODAL */
.modal {
  visibility: hidden;
  position: fixed;
  top: 0px;
  left: 50%;
  z-index: 60;
  height: 100%;
  width: 100%;
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  overflow-y: auto;
  background-color: rgb(0 0 0 / 0.6);
  opacity: 0;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
@media (min-width: 1024px) {

  .ff-modal {
    top: 74px;
  }
}
.info-modal {
  z-index: 70;
}
@media (min-width: 1024px) {

  .modal.ff-modal.active .modal-dialog {
    margin-top: 2.5rem;
    margin-bottom: 8rem;
  }
}
.modal.active {
  visibility: visible;
  opacity: 1;
}
.modal-dialog {
  margin-left: auto;
  margin-right: auto;
  width: 100%;
  max-width: 32rem;
  border-radius: 0.5rem;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.modal.active .modal-dialog {
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
}
.modal-header {
  display: flex;
  justify-content: space-between;
  gap: 1.25rem;
  border-bottom-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));
  padding: 1rem;
}
.modal-title {
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 500;
  text-transform: capitalize;
}
.modal-body {
  padding: 1rem;
}
.modal-btns {
  margin-top: 0.75rem;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  gap: 0.75rem;
}
.modal-btn-outline {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  border-radius: 0.375rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  text-transform: capitalize;
  --tw-text-opacity: 1 !important;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1)) !important;
}
.modal-close {
  font-size: 1.25rem;
  line-height: 1.75rem;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(251 78 78 / var(--tw-text-opacity, 1));
}
/* PAGINATION */
.db-pagination-menu.active {
  background-color: rgb(255 0 107 / 0.2);
  --tw-text-opacity: 1;
  color: rgb(255 0 107 / var(--tw-text-opacity, 1));
}
/* LIST */
.db-list {
  margin-top: -0.375rem;
  margin-bottom: -0.375rem;
  display: flex;
  flex-direction: column;
}
.db-list-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  border-radius: 0.375rem;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
@media (min-width: 640px) {

  .db-list-item {
    flex-direction: row;
    align-items: center;
  }
}
.db-list-item-title {
  white-space: nowrap;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 600;
  text-transform: capitalize;
  --tw-text-opacity: 1;
  color: rgb(31 31 57 / var(--tw-text-opacity, 1));
}
.db-list-item-title::after {
  display: inline-block;
  padding-left: 0.25rem;
  --tw-content: ':';
  content: var(--tw-content);
}
@media (min-width: 640px) {

  .db-list-item-title::after {
    content: var(--tw-content);
    display: none;
  }
}
.db-list-item-title:where([dir="ltr"], [dir="ltr"] *) {
  text-align: left;
}
.db-list-item-title:where([dir="rtl"], [dir="rtl"] *) {
  text-align: right;
}
.db-list-item-text {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.db-list-item-text:where([dir="ltr"], [dir="ltr"] *) {
  text-align: left;
}
.db-list-item-text:where([dir="rtl"], [dir="rtl"] *) {
  text-align: right;
}
.db-list.single .db-list-item-title {
  width: 100%;
  max-width: 100%;
}
@media (min-width: 640px) {

  .db-list.single .db-list-item-title {
    max-width: 180px;
  }
}
.db-list.single .db-list-item-text {
  width: 100%;
}
.db-list.multiple {
  flex-direction: row;
  flex-wrap: wrap;
}
.db-list.multiple .db-list-item {
  width: 100%;
}
@media (min-width: 640px) {

  .db-list.multiple .db-list-item {
    width: 50%;
  }
}
.db-list.multiple .db-list-item-title {
  width: 100%;
}
@media (min-width: 640px) {

  .db-list.multiple .db-list-item-title {
    width: 50%;
  }
}
.db-list.multiple .db-list-item-text {
  width: 100%;
}
@media (min-width: 640px) {

  .db-list.multiple .db-list-item-text {
    width: 50%;
  }
}
/* BADGE */
.db-badge {
  border-radius: 0.25rem;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  font-size: 0.75rem;
  line-height: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}
.db-badge.yellow {
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity, 1));
}
.db-badge.green {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}
.db-badge.blue {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.db-badge.red {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
/* DRAWER */
.drawer {
  position: fixed;
  top: 0px;
  z-index: 50;
  height: 100vh;
  width: 100%;
  max-width: 36rem;
  overflow-y: auto;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
.drawer:where([dir="ltr"], [dir="ltr"] *) {
  right: 0px;
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.drawer:where([dir="rtl"], [dir="rtl"] *) {
  left: 0px;
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.drawer.active:where([dir="ltr"], [dir="ltr"] *) {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.drawer.active:where([dir="rtl"], [dir="rtl"] *) {
  --tw-translate-x: -0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.drawer-header {
  display: flex;
  justify-content: space-between;
  gap: 1.25rem;
  border-bottom-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));
  padding: 1rem;
}
.drawer-title {
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 500;
  text-transform: capitalize;
}
.drawer-body {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  padding-top: 2rem;
  padding-bottom: 2rem;
}
/* DB PRODUCT CARD */
.db-product-cart:hover svg,
    .db-product-cart:hover span {
  fill: #fff;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.db-pos-cartDiv {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}
@media (min-width: 768px) {

  .db-pos-cartDiv {
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
}
.db-pos-cartDiv.active {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.pos-group .active {
  --tw-border-opacity: 1;
  border-color: rgb(255 0 107 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(220 234 255 / var(--tw-bg-opacity, 1));
}
/* FOOTER */
.footer-part {
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 107 / var(--tw-bg-opacity, 1));
}
/* INSTALLER */
.installer-track {
  display: flex;
  width: 109%;
  align-items: center;
  justify-content: space-between;
}
@media (min-width: 640px) {

  .installer-track {
    width: 111%;
  }
}
.installer-track li {
  display: flex;
  width: 100%;
  align-items: center;
}
.installer-track li:last-child::after {
  display: none;
}
.installer-track li:after {
  height: 1px;
  width: 100%;
  --tw-bg-opacity: 1;
  background-color: rgb(217 219 233 / var(--tw-bg-opacity, 1));
  --tw-content: '';
  content: var(--tw-content);
}
.installer-track li i {
  height: 2rem;
  width: 2rem;
  flex-shrink: 0;
  border-radius: 9999px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(217 219 233 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(217 219 233 / var(--tw-bg-opacity, 1));
  text-align: center;
  font-size: 0.75rem;
  line-height: 1rem;
  line-height: 30px;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
@media (min-width: 640px) {

  .installer-track li i {
    height: 38px;
    width: 38px;
    font-size: 0.875rem;
    line-height: 2.25rem;
  }
}
.installer-track li.done {
  cursor: pointer;
}
.installer-track li.done::after {
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 107 / var(--tw-bg-opacity, 1));
}
.installer-track li.done i {
  --tw-border-opacity: 1;
  border-color: rgb(255 0 107 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 0 107 / var(--tw-text-opacity, 1));
}
.installer-track li.active i {
  --tw-border-opacity: 1;
  border-color: rgb(255 0 107 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 107 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.unread-message h4 {
  position: relative;
}
.unread-message h4::after {
  position: absolute;
  top: 0px;
  display: inline-block;
  height: 0.5rem;
  width: 0.5rem;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 107 / var(--tw-bg-opacity, 1));
  --tw-content: '';
  content: var(--tw-content);
}
.unread-message h4:where([dir="ltr"], [dir="ltr"] *)::after {
  content: var(--tw-content);
  right: -0.75rem;
}
.unread-message h4:where([dir="rtl"], [dir="rtl"] *)::after {
  content: var(--tw-content);
  left: -0.75rem;
}
/*===================================
            COMPONENTS PART END
    ====================================*/
.visible {
  visibility: visible;
}
.invisible {
  visibility: hidden;
}
.collapse {
  visibility: collapse;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.\!relative {
  position: relative !important;
}
.relative {
  position: relative;
}
.inset-0 {
  inset: 0px;
}
.-bottom-12 {
  bottom: -3rem;
}
.-left-3 {
  left: -0.75rem;
}
.-right-2 {
  right: -0.5rem;
}
.-top-2 {
  top: -0.5rem;
}
.bottom-0 {
  bottom: 0px;
}
.left-0 {
  left: 0px;
}
.left-1\/2 {
  left: 50%;
}
.right-0 {
  right: 0px;
}
.right-2 {
  right: 0.5rem;
}
.right-2\.5 {
  right: 0.625rem;
}
.right-3 {
  right: 0.75rem;
}
.right-4 {
  right: 1rem;
}
.right-5 {
  right: 1.25rem;
}
.top-0 {
  top: 0px;
}
.top-1\/2 {
  top: 50%;
}
.top-10 {
  top: 2.5rem;
}
.top-12 {
  top: 3rem;
}
.top-14 {
  top: 3.5rem;
}
.top-2 {
  top: 0.5rem;
}
.top-4 {
  top: 1rem;
}
.top-5 {
  top: 1.25rem;
}
.top-9 {
  top: 2.25rem;
}
.top-\[42px\] {
  top: 42px;
}
.top-\[58px\] {
  top: 58px;
}
.top-\[75px\] {
  top: 75px;
}
.-z-10 {
  z-index: -10;
}
.z-0 {
  z-index: 0;
}
.z-10 {
  z-index: 10;
}
.z-20 {
  z-index: 20;
}
.z-30 {
  z-index: 30;
}
.z-50 {
  z-index: 50;
}
.z-60 {
  z-index: 60;
}
.z-\[60\] {
  z-index: 60;
}
.col-span-1 {
  grid-column: span 1 / span 1;
}
.col-span-2 {
  grid-column: span 2 / span 2;
}
.row-span-2 {
  grid-row: span 2 / span 2;
}
.m-0 {
  margin: 0px;
}
.m-0\.5 {
  margin: 0.125rem;
}
.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}
.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-0 {
  margin-top: 0px;
  margin-bottom: 0px;
}
.my-0\.5 {
  margin-top: 0.125rem;
  margin-bottom: 0.125rem;
}
.my-1\.5 {
  margin-top: 0.375rem;
  margin-bottom: 0.375rem;
}
.my-12 {
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.my-3 {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}
.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}
.\!ml-2 {
  margin-left: 0.5rem !important;
}
.-ml-px {
  margin-left: -1px;
}
.-mt-12 {
  margin-top: -3rem;
}
.-mt-7 {
  margin-top: -1.75rem;
}
.mb-0\.5 {
  margin-bottom: 0.125rem;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-1\.5 {
  margin-bottom: 0.375rem;
}
.mb-12 {
  margin-bottom: 3rem;
}
.mb-14 {
  margin-bottom: 3.5rem;
}
.mb-16 {
  margin-bottom: 4rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-2\.5 {
  margin-bottom: 0.625rem;
}
.mb-20 {
  margin-bottom: 5rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-5 {
  margin-bottom: 1.25rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-7 {
  margin-bottom: 1.75rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.mb-9 {
  margin-bottom: 2.25rem;
}
.mb-\[18px\] {
  margin-bottom: 18px;
}
.mb-\[1px\] {
  margin-bottom: 1px;
}
.mb-\[22px\] {
  margin-bottom: 22px;
}
.mb-\[70px\] {
  margin-bottom: 70px;
}
.ml-1\.5 {
  margin-left: 0.375rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-3 {
  margin-left: 0.75rem;
}
.ml-auto {
  margin-left: auto;
}
.mr-1 {
  margin-right: 0.25rem;
}
.mr-4 {
  margin-right: 1rem;
}
.mt-0\.5 {
  margin-top: 0.125rem;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-1\.5 {
  margin-top: 0.375rem;
}
.mt-12 {
  margin-top: 3rem;
}
.mt-16 {
  margin-top: 4rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-3 {
  margin-top: 0.75rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-5 {
  margin-top: 1.25rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.mt-8 {
  margin-top: 2rem;
}
.mt-\[-20px\] {
  margin-top: -20px;
}
.block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.\!flex {
  display: flex !important;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.table {
  display: table;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.\!h-40 {
  height: 10rem !important;
}
.h-0 {
  height: 0px;
}
.h-10 {
  height: 2.5rem;
}
.h-11 {
  height: 2.75rem;
}
.h-12 {
  height: 3rem;
}
.h-14 {
  height: 3.5rem;
}
.h-16 {
  height: 4rem;
}
.h-2 {
  height: 0.5rem;
}
.h-20 {
  height: 5rem;
}
.h-3 {
  height: 0.75rem;
}
.h-32 {
  height: 8rem;
}
.h-4 {
  height: 1rem;
}
.h-5 {
  height: 1.25rem;
}
.h-6 {
  height: 1.5rem;
}
.h-7 {
  height: 1.75rem;
}
.h-8 {
  height: 2rem;
}
.h-80 {
  height: 20rem;
}
.h-9 {
  height: 2.25rem;
}
.h-\[102px\] {
  height: 102px;
}
.h-\[120px\] {
  height: 120px;
}
.h-\[180px\] {
  height: 180px;
}
.h-\[18px\] {
  height: 18px;
}
.h-\[1px\] {
  height: 1px;
}
.h-\[26px\] {
  height: 26px;
}
.h-\[300px\] {
  height: 300px;
}
.h-\[37px\] {
  height: 37px;
}
.h-\[38px\] {
  height: 38px;
}
.h-\[400px\] {
  height: 400px;
}
.h-\[50px\] {
  height: 50px;
}
.h-\[52px\] {
  height: 52px;
}
.h-\[68px\] {
  height: 68px;
}
.h-\[70px\] {
  height: 70px;
}
.h-\[72px\] {
  height: 72px;
}
.h-\[90px\] {
  height: 90px;
}
.h-\[98px\] {
  height: 98px;
}
.h-\[calc\(100vh-250px\)\] {
  height: calc(100vh - 250px);
}
.h-\[calc\(100vh_-_75px\)\] {
  height: calc(100vh - 75px);
}
.h-\[calc\(96vh-200px\)\] {
  height: calc(96vh - 200px);
}
.h-auto {
  height: auto;
}
.h-fit {
  height: -moz-fit-content;
  height: fit-content;
}
.h-full {
  height: 100%;
}
.h-screen {
  height: 100vh;
}
.\!w-fit {
  width: -moz-fit-content !important;
  width: fit-content !important;
}
.w-10 {
  width: 2.5rem;
}
.w-11 {
  width: 2.75rem;
}
.w-12 {
  width: 3rem;
}
.w-16 {
  width: 4rem;
}
.w-20 {
  width: 5rem;
}
.w-24 {
  width: 6rem;
}
.w-28 {
  width: 7rem;
}
.w-3 {
  width: 0.75rem;
}
.w-32 {
  width: 8rem;
}
.w-36 {
  width: 9rem;
}
.w-4 {
  width: 1rem;
}
.w-40 {
  width: 10rem;
}
.w-48 {
  width: 12rem;
}
.w-5 {
  width: 1.25rem;
}
.w-6 {
  width: 1.5rem;
}
.w-7 {
  width: 1.75rem;
}
.w-8 {
  width: 2rem;
}
.w-80 {
  width: 20rem;
}
.w-9 {
  width: 2.25rem;
}
.w-\[120px\] {
  width: 120px;
}
.w-\[150px\] {
  width: 150px;
}
.w-\[18px\] {
  width: 18px;
}
.w-\[26px\] {
  width: 26px;
}
.w-\[38px\] {
  width: 38px;
}
.w-\[46px\] {
  width: 46px;
}
.w-\[550px\] {
  width: 550px;
}
.w-\[68px\] {
  width: 68px;
}
.w-\[72px\] {
  width: 72px;
}
.w-\[76px\] {
  width: 76px;
}
.w-\[90px\] {
  width: 90px;
}
.w-\[98px\] {
  width: 98px;
}
.w-fit {
  width: -moz-fit-content;
  width: fit-content;
}
.w-full {
  width: 100%;
}
.w-screen {
  width: 100vw;
}
.min-w-\[120px\] {
  min-width: 120px;
}
.min-w-\[150px\] {
  min-width: 150px;
}
.min-w-\[162px\] {
  min-width: 162px;
}
.min-w-\[180px\] {
  min-width: 180px;
}
.min-w-\[200px\] {
  min-width: 200px;
}
.min-w-\[97px\] {
  min-width: 97px;
}
.max-w-2xl {
  max-width: 42rem;
}
.max-w-3xl {
  max-width: 48rem;
}
.max-w-\[100px\] {
  max-width: 100px;
}
.max-w-\[120px\] {
  max-width: 120px;
}
.max-w-\[148px\] {
  max-width: 148px;
}
.max-w-\[200px\] {
  max-width: 200px;
}
.max-w-\[220px\] {
  max-width: 220px;
}
.max-w-\[250px\] {
  max-width: 250px;
}
.max-w-\[265px\] {
  max-width: 265px;
}
.max-w-\[300px\] {
  max-width: 300px;
}
.max-w-\[340px\] {
  max-width: 340px;
}
.max-w-\[350px\] {
  max-width: 350px;
}
.max-w-\[360px\] {
  max-width: 360px;
}
.max-w-\[428px\] {
  max-width: 428px;
}
.max-w-\[550px\] {
  max-width: 550px;
}
.max-w-\[630px\] {
  max-width: 630px;
}
.max-w-\[647px\] {
  max-width: 647px;
}
.max-w-\[965px\] {
  max-width: 965px;
}
.max-w-sm {
  max-width: 24rem;
}
.max-w-xs {
  max-width: 20rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-auto {
  flex: 1 1 auto;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.flex-grow {
  flex-grow: 1;
}
.table-auto {
  table-layout: auto;
}
.border-collapse {
  border-collapse: collapse;
}
.origin-top {
  transform-origin: top;
}
.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-\[0\.98\] {
  --tw-scale-x: 0.98;
  --tw-scale-y: 0.98;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-y-0 {
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.cursor-default {
  cursor: default;
}
.cursor-move {
  cursor: move;
}
.cursor-pointer {
  cursor: pointer;
}
.resize-none {
  resize: none;
}
.appearance-none {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-10 {
  grid-template-columns: repeat(10, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.grid-cols-\[repeat\(auto-fill\2c _minmax\(140px\2c _1fr\)\)\] {
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
}
.flex-col {
  flex-direction: column;
}
.flex-col-reverse {
  flex-direction: column-reverse;
}
.flex-wrap {
  flex-wrap: wrap;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.items-center {
  align-items: center;
}
.\!justify-start {
  justify-content: flex-start !important;
}
.justify-start {
  justify-content: flex-start;
}
.justify-end {
  justify-content: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.justify-items-center {
  justify-items: center;
}
.gap-1 {
  gap: 0.25rem;
}
.gap-1\.5 {
  gap: 0.375rem;
}
.gap-11 {
  gap: 2.75rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-2\.5 {
  gap: 0.625rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-3\.5 {
  gap: 0.875rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-5 {
  gap: 1.25rem;
}
.gap-6 {
  gap: 1.5rem;
}
.gap-8 {
  gap: 2rem;
}
.gap-\[18px\] {
  gap: 18px;
}
.gap-x-3 {
  -moz-column-gap: 0.75rem;
       column-gap: 0.75rem;
}
.gap-x-4 {
  -moz-column-gap: 1rem;
       column-gap: 1rem;
}
.gap-x-6 {
  -moz-column-gap: 1.5rem;
       column-gap: 1.5rem;
}
.gap-y-1 {
  row-gap: 0.25rem;
}
.gap-y-2 {
  row-gap: 0.5rem;
}
.gap-y-3\.5 {
  row-gap: 0.875rem;
}
.gap-y-4 {
  row-gap: 1rem;
}
.gap-y-5 {
  row-gap: 1.25rem;
}
.overflow-auto {
  overflow: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-y-auto {
  overflow-y: auto;
}
.text-ellipsis {
  text-overflow: ellipsis;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.text-wrap {
  text-wrap: wrap;
}
.break-words {
  overflow-wrap: break-word;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-2xl {
  border-radius: 1rem;
}
.rounded-3xl {
  border-radius: 1.5rem;
}
.rounded-\[10px\] {
  border-radius: 10px;
}
.rounded-\[3px\] {
  border-radius: 3px;
}
.rounded-\[4px\] {
  border-radius: 4px;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded-md {
  border-radius: 0.375rem;
}
.rounded-none {
  border-radius: 0px;
}
.rounded-xl {
  border-radius: 0.75rem;
}
.rounded-b-2xl {
  border-bottom-right-radius: 1rem;
  border-bottom-left-radius: 1rem;
}
.rounded-b-\[11px\] {
  border-bottom-right-radius: 11px;
  border-bottom-left-radius: 11px;
}
.rounded-l-\[11px\] {
  border-top-left-radius: 11px;
  border-bottom-left-radius: 11px;
}
.rounded-l-lg {
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.rounded-l-md {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}
.rounded-r-lg {
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}
.rounded-r-md {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}
.rounded-t {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.rounded-t-2xl {
  border-top-left-radius: 1rem;
  border-top-right-radius: 1rem;
}
.rounded-t-\[10px\] {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}
.rounded-t-\[11px\] {
  border-top-left-radius: 11px;
  border-top-right-radius: 11px;
}
.rounded-t-lg {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}
.rounded-bl {
  border-bottom-left-radius: 0.25rem;
}
.rounded-bl-lg {
  border-bottom-left-radius: 0.5rem;
}
.rounded-br-lg {
  border-bottom-right-radius: 0.5rem;
}
.rounded-tl {
  border-top-left-radius: 0.25rem;
}
.rounded-tl-lg {
  border-top-left-radius: 0.5rem;
}
.rounded-tr-lg {
  border-top-right-radius: 0.5rem;
}
.border {
  border-width: 1px;
}
.border-2 {
  border-width: 2px;
}
.border-y {
  border-top-width: 1px;
  border-bottom-width: 1px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-b-2 {
  border-bottom-width: 2px;
}
.border-l {
  border-left-width: 1px;
}
.border-t {
  border-top-width: 1px;
}
.border-solid {
  border-style: solid;
}
.border-dashed {
  border-style: dashed;
}
.border-none {
  border-style: none;
}
.\!border-\[\#FB4E4E\] {
  --tw-border-opacity: 1 !important;
  border-color: rgb(251 78 78 / var(--tw-border-opacity, 1)) !important;
}
.border-\[\#6E7191\] {
  --tw-border-opacity: 1;
  border-color: rgb(110 113 145 / var(--tw-border-opacity, 1));
}
.border-\[\#D9DBE9\] {
  --tw-border-opacity: 1;
  border-color: rgb(217 219 233 / var(--tw-border-opacity, 1));
}
.border-\[\#DBDEE0\] {
  --tw-border-opacity: 1;
  border-color: rgb(219 222 224 / var(--tw-border-opacity, 1));
}
.border-\[\#EFF0F6\] {
  --tw-border-opacity: 1;
  border-color: rgb(239 240 246 / var(--tw-border-opacity, 1));
}
.border-\[\#F7F7FC\] {
  --tw-border-opacity: 1;
  border-color: rgb(247 247 252 / var(--tw-border-opacity, 1));
}
.border-\[\#ff3388\] {
  --tw-border-opacity: 1;
  border-color: rgb(255 51 136 / var(--tw-border-opacity, 1));
}
.border-blue-500 {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.border-gray-100 {
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));
}
.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.border-gray-400 {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}
.border-primary {
  --tw-border-opacity: 1;
  border-color: rgb(255 0 107 / var(--tw-border-opacity, 1));
}
.border-primary\/30 {
  border-color: rgb(255 0 107 / 0.3);
}
.border-primary\/40 {
  border-color: rgb(255 0 107 / 0.4);
}
.border-red-400 {
  --tw-border-opacity: 1;
  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));
}
.border-slate-100 {
  --tw-border-opacity: 1;
  border-color: rgb(241 245 249 / var(--tw-border-opacity, 1));
}
.border-slate-200 {
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));
}
.border-transparent {
  border-color: transparent;
}
.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.\!bg-primary\/5 {
  background-color: rgb(255 0 107 / 0.05) !important;
}
.\!bg-white {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1)) !important;
}
.bg-\[\#00749B\] {
  --tw-bg-opacity: 1;
  background-color: rgb(0 116 155 / var(--tw-bg-opacity, 1));
}
.bg-\[\#008BBA\] {
  --tw-bg-opacity: 1;
  background-color: rgb(0 139 186 / var(--tw-bg-opacity, 1));
}
.bg-\[\#1AB759\] {
  --tw-bg-opacity: 1;
  background-color: rgb(26 183 89 / var(--tw-bg-opacity, 1));
}
.bg-\[\#2AC769\] {
  --tw-bg-opacity: 1;
  background-color: rgb(42 199 105 / var(--tw-bg-opacity, 1));
}
.bg-\[\#567DFF\] {
  --tw-bg-opacity: 1;
  background-color: rgb(86 125 255 / var(--tw-bg-opacity, 1));
}
.bg-\[\#8262FE\] {
  --tw-bg-opacity: 1;
  background-color: rgb(130 98 254 / var(--tw-bg-opacity, 1));
}
.bg-\[\#A0A3BD\] {
  --tw-bg-opacity: 1;
  background-color: rgb(160 163 189 / var(--tw-bg-opacity, 1));
}
.bg-\[\#A953FF\] {
  --tw-bg-opacity: 1;
  background-color: rgb(169 83 255 / var(--tw-bg-opacity, 1));
}
.bg-\[\#BDEFFF\] {
  --tw-bg-opacity: 1;
  background-color: rgb(189 239 255 / var(--tw-bg-opacity, 1));
}
.bg-\[\#CBFFE0\] {
  --tw-bg-opacity: 1;
  background-color: rgb(203 255 224 / var(--tw-bg-opacity, 1));
}
.bg-\[\#D6F5FF\] {
  --tw-bg-opacity: 1;
  background-color: rgb(214 245 255 / var(--tw-bg-opacity, 1));
}
.bg-\[\#E0FFED\] {
  --tw-bg-opacity: 1;
  background-color: rgb(224 255 237 / var(--tw-bg-opacity, 1));
}
.bg-\[\#E7FFF0\] {
  --tw-bg-opacity: 1;
  background-color: rgb(231 255 240 / var(--tw-bg-opacity, 1));
}
.bg-\[\#E9EEFF\] {
  --tw-bg-opacity: 1;
  background-color: rgb(233 238 255 / var(--tw-bg-opacity, 1));
}
.bg-\[\#E9F9FF\] {
  --tw-bg-opacity: 1;
  background-color: rgb(233 249 255 / var(--tw-bg-opacity, 1));
}
.bg-\[\#EBE7FF\] {
  --tw-bg-opacity: 1;
  background-color: rgb(235 231 255 / var(--tw-bg-opacity, 1));
}
.bg-\[\#EFF0F6\] {
  --tw-bg-opacity: 1;
  background-color: rgb(239 240 246 / var(--tw-bg-opacity, 1));
}
.bg-\[\#F0F8FF\] {
  --tw-bg-opacity: 1;
  background-color: rgb(240 248 255 / var(--tw-bg-opacity, 1));
}
.bg-\[\#F5EAFF\] {
  --tw-bg-opacity: 1;
  background-color: rgb(245 234 255 / var(--tw-bg-opacity, 1));
}
.bg-\[\#F6A609\] {
  --tw-bg-opacity: 1;
  background-color: rgb(246 166 9 / var(--tw-bg-opacity, 1));
}
.bg-\[\#F7F7F7\] {
  --tw-bg-opacity: 1;
  background-color: rgb(247 247 247 / var(--tw-bg-opacity, 1));
}
.bg-\[\#F7F7FC\] {
  --tw-bg-opacity: 1;
  background-color: rgb(247 247 252 / var(--tw-bg-opacity, 1));
}
.bg-\[\#FAF4FF\] {
  --tw-bg-opacity: 1;
  background-color: rgb(250 244 255 / var(--tw-bg-opacity, 1));
}
.bg-\[\#FB4E4E\] {
  --tw-bg-opacity: 1;
  background-color: rgb(251 78 78 / var(--tw-bg-opacity, 1));
}
.bg-\[\#FF4F99\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 79 153 / var(--tw-bg-opacity, 1));
}
.bg-\[\#FFD7E7\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 215 231 / var(--tw-bg-opacity, 1));
}
.bg-\[\#FFDADA\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 218 218 / var(--tw-bg-opacity, 1));
}
.bg-\[\#FFDB1F\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 219 31 / var(--tw-bg-opacity, 1));
}
.bg-\[\#FFE6F0\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 230 240 / var(--tw-bg-opacity, 1));
}
.bg-\[\#FFEAEA\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 234 234 / var(--tw-bg-opacity, 1));
}
.bg-\[\#FFEBD8\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 235 216 / var(--tw-bg-opacity, 1));
}
.bg-\[\#FFEDF4\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 244 / var(--tw-bg-opacity, 1));
}
.bg-\[\#FFEEC6\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 238 198 / var(--tw-bg-opacity, 1));
}
.bg-\[\#FFF5DE\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 245 222 / var(--tw-bg-opacity, 1));
}
.bg-\[\#FFF6E6\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 246 230 / var(--tw-bg-opacity, 1));
}
.bg-\[\#FFF6EE\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 246 238 / var(--tw-bg-opacity, 1));
}
.bg-\[\#e5ebff\] {
  --tw-bg-opacity: 1;
  background-color: rgb(229 235 255 / var(--tw-bg-opacity, 1));
}
.bg-\[\#fcfcfc\] {
  --tw-bg-opacity: 1;
  background-color: rgb(252 252 252 / var(--tw-bg-opacity, 1));
}
.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.bg-black\/60 {
  background-color: rgb(0 0 0 / 0.6);
}
.bg-blue-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}
.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.bg-emerald-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(16 185 129 / var(--tw-bg-opacity, 1));
}
.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-gray-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}
.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}
.bg-heading {
  --tw-bg-opacity: 1;
  background-color: rgb(31 31 57 / var(--tw-bg-opacity, 1));
}
.bg-orange-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
}
.bg-orange-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}
.bg-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 107 / var(--tw-bg-opacity, 1));
}
.bg-primary\/10 {
  background-color: rgb(255 0 107 / 0.1);
}
.bg-primary\/5 {
  background-color: rgb(255 0 107 / 0.05);
}
.bg-purple-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}
.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}
.bg-sky-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(2 132 199 / var(--tw-bg-opacity, 1));
}
.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-white\/90 {
  background-color: rgb(255 255 255 / 0.9);
}
.bg-gradient-to-t {
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}
.bg-installer {
  background-image: url('/themes/default/images/bg/installer.jpg');
}
.from-\[\#FF7A00\] {
  --tw-gradient-from: #FF7A00 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 122 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.to-\[\#FF016C\] {
  --tw-gradient-to: #FF016C var(--tw-gradient-to-position);
}
.bg-cover {
  background-size: cover;
}
.bg-center {
  background-position: center;
}
.bg-no-repeat {
  background-repeat: no-repeat;
}
.fill-\[\#00749B\] {
  fill: #00749B;
}
.stroke-\[\#6E7191\] {
  stroke: #6E7191;
}
.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}
.object-fill {
  -o-object-fit: fill;
     object-fit: fill;
}
.\!p-2\.5 {
  padding: 0.625rem !important;
}
.p-0 {
  padding: 0px;
}
.p-0\.5 {
  padding: 0.125rem;
}
.p-1 {
  padding: 0.25rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-2\.5 {
  padding: 0.625rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-4 {
  padding: 1rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-8 {
  padding: 2rem;
}
.\!py-1\.5 {
  padding-top: 0.375rem !important;
  padding-bottom: 0.375rem !important;
}
.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-3\.5 {
  padding-left: 0.875rem;
  padding-right: 0.875rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-7 {
  padding-left: 1.75rem;
  padding-right: 1.75rem;
}
.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}
.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-14 {
  padding-top: 3.5rem;
  padding-bottom: 3.5rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-3\.5 {
  padding-top: 0.875rem;
  padding-bottom: 0.875rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}
.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.pb-0 {
  padding-bottom: 0px;
}
.pb-1 {
  padding-bottom: 0.25rem;
}
.pb-12 {
  padding-bottom: 3rem;
}
.pb-16 {
  padding-bottom: 4rem;
}
.pb-2 {
  padding-bottom: 0.5rem;
}
.pb-20 {
  padding-bottom: 5rem;
}
.pb-3 {
  padding-bottom: 0.75rem;
}
.pb-3\.5 {
  padding-bottom: 0.875rem;
}
.pb-4 {
  padding-bottom: 1rem;
}
.pl-2 {
  padding-left: 0.5rem;
}
.pl-3 {
  padding-left: 0.75rem;
}
.pl-4 {
  padding-left: 1rem;
}
.pl-5 {
  padding-left: 1.25rem;
}
.pl-7 {
  padding-left: 1.75rem;
}
.pl-\[18px\] {
  padding-left: 18px;
}
.pr-1 {
  padding-right: 0.25rem;
}
.pr-2\.5 {
  padding-right: 0.625rem;
}
.pr-4 {
  padding-right: 1rem;
}
.pr-6 {
  padding-right: 1.5rem;
}
.pt-1 {
  padding-top: 0.25rem;
}
.pt-12 {
  padding-top: 3rem;
}
.pt-16 {
  padding-top: 4rem;
}
.pt-2 {
  padding-top: 0.5rem;
}
.pt-3 {
  padding-top: 0.75rem;
}
.pt-4 {
  padding-top: 1rem;
}
.pt-5 {
  padding-top: 1.25rem;
}
.pt-7 {
  padding-top: 1.75rem;
}
.pt-8 {
  padding-top: 2rem;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.text-start {
  text-align: start;
}
.text-end {
  text-align: end;
}
.align-top {
  vertical-align: top;
}
.font-public {
  font-family: 'Public Sans', sans-serif;
}
.font-rubik {
  font-family: 'Rubik', sans-serif;
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-\[10px\] {
  font-size: 10px;
}
.text-\[11px\] {
  font-size: 11px;
}
.text-\[15px\] {
  font-size: 15px;
}
.text-\[18px\] {
  font-size: 18px;
}
.text-\[22px\] {
  font-size: 22px;
}
.text-\[26px\] {
  font-size: 26px;
}
.text-\[8px\] {
  font-size: 8px;
}
.text-\[9px\] {
  font-size: 9px;
}
.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-\[300\] {
  font-weight: 300;
}
.font-bold {
  font-weight: 700;
}
.font-light {
  font-weight: 300;
}
.font-medium {
  font-weight: 500;
}
.font-normal {
  font-weight: 400;
}
.font-semibold {
  font-weight: 600;
}
.uppercase {
  text-transform: uppercase;
}
.lowercase {
  text-transform: lowercase;
}
.capitalize {
  text-transform: capitalize;
}
.normal-case {
  text-transform: none;
}
.leading-10 {
  line-height: 2.5rem;
}
.leading-3 {
  line-height: .75rem;
}
.leading-4 {
  line-height: 1rem;
}
.leading-5 {
  line-height: 1.25rem;
}
.leading-6 {
  line-height: 1.5rem;
}
.leading-7 {
  line-height: 1.75rem;
}
.leading-8 {
  line-height: 2rem;
}
.leading-\[10px\] {
  line-height: 10px;
}
.leading-\[14px\] {
  line-height: 14px;
}
.leading-\[16px\] {
  line-height: 16px;
}
.leading-\[17px\] {
  line-height: 17px;
}
.leading-\[26px\] {
  line-height: 26px;
}
.leading-\[34px\] {
  line-height: 34px;
}
.leading-\[38px\] {
  line-height: 38px;
}
.leading-\[40px\] {
  line-height: 40px;
}
.leading-\[46px\] {
  line-height: 46px;
}
.leading-\[48px\] {
  line-height: 48px;
}
.leading-\[9px\] {
  line-height: 9px;
}
.leading-none {
  line-height: 1;
}
.tracking-wide {
  letter-spacing: 0.025em;
}
.\!text-\[\#008BBA\] {
  --tw-text-opacity: 1 !important;
  color: rgb(0 139 186 / var(--tw-text-opacity, 1)) !important;
}
.\!text-\[\#FB4E4E\] {
  --tw-text-opacity: 1 !important;
  color: rgb(251 78 78 / var(--tw-text-opacity, 1)) !important;
}
.text-\[\#00749B\] {
  --tw-text-opacity: 1;
  color: rgb(0 116 155 / var(--tw-text-opacity, 1));
}
.text-\[\#0084FF\] {
  --tw-text-opacity: 1;
  color: rgb(0 132 255 / var(--tw-text-opacity, 1));
}
.text-\[\#008BBA\] {
  --tw-text-opacity: 1;
  color: rgb(0 139 186 / var(--tw-text-opacity, 1));
}
.text-\[\#1AB759\] {
  --tw-text-opacity: 1;
  color: rgb(26 183 89 / var(--tw-text-opacity, 1));
}
.text-\[\#1F1F39\] {
  --tw-text-opacity: 1;
  color: rgb(31 31 57 / var(--tw-text-opacity, 1));
}
.text-\[\#2AC769\] {
  --tw-text-opacity: 1;
  color: rgb(42 199 105 / var(--tw-text-opacity, 1));
}
.text-\[\#2E2F38\] {
  --tw-text-opacity: 1;
  color: rgb(46 47 56 / var(--tw-text-opacity, 1));
}
.text-\[\#374151\] {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-\[\#567DFF\] {
  --tw-text-opacity: 1;
  color: rgb(86 125 255 / var(--tw-text-opacity, 1));
}
.text-\[\#6E7191\] {
  --tw-text-opacity: 1;
  color: rgb(110 113 145 / var(--tw-text-opacity, 1));
}
.text-\[\#9837FF\] {
  --tw-text-opacity: 1;
  color: rgb(152 55 255 / var(--tw-text-opacity, 1));
}
.text-\[\#A0A3BD\] {
  --tw-text-opacity: 1;
  color: rgb(160 163 189 / var(--tw-text-opacity, 1));
}
.text-\[\#A953FF\] {
  --tw-text-opacity: 1;
  color: rgb(169 83 255 / var(--tw-text-opacity, 1));
}
.text-\[\#E89806\] {
  --tw-text-opacity: 1;
  color: rgb(232 152 6 / var(--tw-text-opacity, 1));
}
.text-\[\#E93C3C\] {
  --tw-text-opacity: 1;
  color: rgb(233 60 60 / var(--tw-text-opacity, 1));
}
.text-\[\#F6A609\] {
  --tw-text-opacity: 1;
  color: rgb(246 166 9 / var(--tw-text-opacity, 1));
}
.text-\[\#FB4E4E\] {
  --tw-text-opacity: 1;
  color: rgb(251 78 78 / var(--tw-text-opacity, 1));
}
.text-\[\#FF8C1A\] {
  --tw-text-opacity: 1;
  color: rgb(255 140 26 / var(--tw-text-opacity, 1));
}
.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}
.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}
.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}
.text-heading {
  --tw-text-opacity: 1;
  color: rgb(31 31 57 / var(--tw-text-opacity, 1));
}
.text-orange-500 {
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity, 1));
}
.text-paragraph {
  --tw-text-opacity: 1;
  color: rgb(110 113 145 / var(--tw-text-opacity, 1));
}
.text-primary {
  --tw-text-opacity: 1;
  color: rgb(255 0 107 / var(--tw-text-opacity, 1));
}
.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.text-red-700 {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}
.text-slate-400 {
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity, 1));
}
.text-slate-500 {
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}
.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.underline {
  text-decoration-line: underline;
}
.accent-primary {
  accent-color: rgb(255 0 107 / 1);
}
.opacity-0 {
  opacity: 0;
}
.shadow-\[0px_6px_10px_rgba\(251\2c _78\2c _78\2c _0\.24\)\] {
  --tw-shadow: 0px 6px 10px rgba(251, 78, 78, 0.24);
  --tw-shadow-colored: 0px 6px 10px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0px_6px_10px_rgba\(255\2c _0\2c _107\2c _0\.24\)\] {
  --tw-shadow: 0px 6px 10px rgba(255, 0, 107, 0.24);
  --tw-shadow-colored: 0px 6px 10px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0px_6px_10px_rgba\(26\2c _183\2c _89\2c _0\.24\)\] {
  --tw-shadow: 0px 6px 10px rgba(26, 183, 89, 0.24);
  --tw-shadow-colored: 0px 6px 10px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-avatar {
  --tw-shadow: 0px 6px 10px rgba(23 114 255, 0.15);
  --tw-shadow-colored: 0px 6px 10px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-cardCart {
  --tw-shadow: 0px 8px 16px rgba(23, 31, 70, 0.08);
  --tw-shadow-colored: 0px 8px 16px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-coupon {
  --tw-shadow: 0px 4px 8px rgba(0, 0, 0, 0.04), 0px 0px 2px rgba(0, 0, 0, 0.06), 0px 0px 1px rgba(0, 0, 0, 0.04);
  --tw-shadow-colored: 0px 4px 8px var(--tw-shadow-color), 0px 0px 2px var(--tw-shadow-color), 0px 0px 1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-db-card {
  --tw-shadow: 0 2px 6px 0 rgb(67 89 113 / 12%);
  --tw-shadow-colored: 0 2px 6px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-paper {
  --tw-shadow: 0px 4px 40px rgba(23, 31, 70, 0.16);
  --tw-shadow-colored: 0px 4px 40px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl-top {
  --tw-shadow: 0 -20px 25px -5px rgb(0 0 0 / 0.1), 0 -8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 -20px 25px -5px var(--tw-shadow-color), 0 -8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xs {
  --tw-shadow: 0px 6px 32px 0px rgba(0, 0, 0, 0.04);
  --tw-shadow-colored: 0px 6px 32px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline {
  outline-style: solid;
}
.ring-gray-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(209 213 219 / var(--tw-ring-opacity, 1));
}
.drop-shadow-category {
  --tw-drop-shadow: drop-shadow(2px 4px 8px rgba(0, 0, 0, 0.25));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-150 {
  transition-duration: 150ms;
}
.duration-300 {
  transition-duration: 300ms;
}
.duration-500 {
  transition-duration: 500ms;
}
.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.transition {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: linear;
}

.swiper:hover .banner-swiper-navigate {
  opacity: 1;
}
.swiper-pagination-bullet {
  margin: 0px !important;
  height: 1.25rem;
  width: 1.25rem;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  text-align: center;
  font-size: 0.75rem;
  font-weight: 400;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(110 113 145 / var(--tw-text-opacity, 1));
  opacity: 1;
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.swiper-pagination-bullet-active {
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 107 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

/* BANNER */
.banner-swiper .swiper-button-prev {
  visibility: hidden;
  position: absolute;
  top: 50%;
  left: 1.75rem !important;
  right: inherit !important;
  z-index: 50;
  display: flex;
  height: 2rem;
  width: 2rem;
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  cursor: pointer;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  text-align: center;
  opacity: 0;
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}
.banner-swiper .swiper-button-prev::after {
  font-family: 'Font Awesome 6 Free';
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 700;
  --tw-content: '\f053';
  content: var(--tw-content);
}
.banner-swiper .swiper-button-prev:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 107 / var(--tw-bg-opacity, 1));
}
.banner-swiper .swiper-button-prev:hover::after {
  content: var(--tw-content);
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
@media (min-width: 640px) {

  .banner-swiper .swiper-button-prev {
    height: 2.5rem;
    width: 2.5rem;
  }
}
.banner-swiper .swiper-button-next {
  visibility: hidden;
  position: absolute;
  top: 50%;
  right: 1.75rem !important;
  left: inherit !important;
  z-index: 50;
  display: flex;
  height: 2rem;
  width: 2rem;
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  cursor: pointer;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  text-align: center;
  opacity: 0;
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}
.banner-swiper .swiper-button-next::after {
  font-family: 'Font Awesome 6 Free';
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 700;
  --tw-content: '\f054';
  content: var(--tw-content);
}
.banner-swiper .swiper-button-next:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 107 / var(--tw-bg-opacity, 1));
}
.banner-swiper .swiper-button-next:hover::after {
  content: var(--tw-content);
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
@media (min-width: 640px) {

  .banner-swiper .swiper-button-next {
    height: 2.5rem;
    width: 2.5rem;
  }
}
.banner-swiper:hover .swiper-button-prev,
.banner-swiper:hover .swiper-button-next {
  visibility: visible;
  opacity: 1;
}
.banner-swiper .swiper-pagination {
  position: absolute;
  right: 50%;
  bottom: 1.25rem !important;
  z-index: 50;
  display: flex;
  --tw-translate-x: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  align-items: center;
  justify-content: center;
  gap: 0.625rem;
}
@media (min-width: 640px) {

  .banner-swiper .swiper-pagination {
    bottom: 1.75rem !important;
  }
}
.banner-swiper .swiper-pagination-bullet {
  margin: 0px !important;
  height: 0.375rem;
  width: 0.75rem;
  flex-shrink: 0;
  cursor: pointer;
  border-radius: 0.75rem;
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 107 / var(--tw-bg-opacity, 1));
  opacity: 0.3;
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}
.banner-swiper .swiper-pagination-bullet-active {
  width: 1rem;
  flex-shrink: 0;
  opacity: 1;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0; 
}

input[type=number] {
    -moz-appearance:textfield; 
}

input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-results-button,
input[type="search"]::-webkit-search-results-decoration { display: none; }

.apexcharts-tooltip {
  overflow: hidden;
  border-radius: 0.375rem !important;
  border-width: 1px !important;
  --tw-border-opacity: 1 !important;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1)) !important;
  --tw-shadow: 0px 4px 40px rgba(23, 31, 70, 0.16) !important;
  --tw-shadow-colored: 0px 4px 40px var(--tw-shadow-color) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}
.apexcharts-tooltip-z-group,
.apexcharts-tooltip-goals-group {
  display: none !important;
}
.apexcharts-tooltip-title {
  margin-bottom: 0px !important;
  border-bottom-width: 1px !important;
  --tw-border-opacity: 1 !important;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1)) !important;
  padding-top: 0.375rem !important;
  padding-bottom: 0.375rem !important;
  padding-left: 0.75rem !important;
  padding-right: 0.75rem !important;
}
.apexcharts-tooltip-series-group {
  padding-top: 0.375rem !important;
  padding-bottom: 0.375rem !important;
  padding-left: 0.75rem !important;
  padding-right: 0.75rem !important;
}

.time-slot-gap { margin: 12px; }
.btnr {
  display: flex;
  width: 100%;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  --tw-shadow: 0px 3px 8px rgba(0,0,0,0.24);
  --tw-shadow-colored: 0px 3px 8px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
#cursor {
  display: inline-block;
  padding-top: 0.625rem;
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.btnr:active {
  --tw-bg-opacity: 1;
  background-color: rgb(177 176 176 / var(--tw-bg-opacity, 1));
}
.btnr:hover {
  background-color: #ff006b0d;
}
.num:hover {
  background-color: #ff006b0d;
}
.btnr::-moz-selection {
    -moz-user-select: none;
         user-select: none;
}
.btnr::selection {
    -webkit-user-select: none;
       -moz-user-select: none;
            user-select: none;
}
.cs-custom-switcher {
  display: inline-flex;
  flex-shrink: 0;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
}
.cs-custom-switcher input {
  position: relative;
  height: 1.5rem;
  width: 2.75rem;
  flex-shrink: 0;
  cursor: pointer;
}
.cs-custom-switcher input::before {
  position: absolute;
  inset: 0px;
  display: inline-block;
  height: 100%;
  width: 100%;
  border-radius: 1.5rem;
  --tw-bg-opacity: 1;
  background-color: rgb(110 113 145 / var(--tw-bg-opacity, 1));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  --tw-content: '';
  content: var(--tw-content);
}
.cs-custom-switcher input::after {
  position: absolute;
  top: 50%;
  height: 1.25rem;
  width: 1.25rem;
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  border-radius: 9999px;
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
  --tw-gradient-from: #fff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: rgb(255 255 255 / 0.7) var(--tw-gradient-to-position);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  --tw-content: '';
  content: var(--tw-content);
}
.cs-custom-switcher input:where([dir="ltr"], [dir="ltr"] *)::after {
  left: 26%;
  content: var(--tw-content);
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.cs-custom-switcher input:where([dir="rtl"], [dir="rtl"] *)::after {
  right: 26%;
  content: var(--tw-content);
  --tw-translate-x: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.cs-custom-switcher input:checked::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 107 / var(--tw-bg-opacity, 1));
}
.cs-custom-switcher input:checked:where([dir="ltr"], [dir="ltr"] *)::after {
  content: var(--tw-content);
  left: 72%;
}
.cs-custom-switcher input:checked:where([dir="rtl"], [dir="rtl"] *)::after {
  content: var(--tw-content);
  right: 72%;
}
@media (min-width: 640px) {
    .sm\:col-5 {
    width: 40%;
    padding: 0.75rem;
  }
    .sm\:col-6 {
    width: 50%;
    padding: 0.75rem;
  }
    .sm\:col-7 {
    width: 60%;
    padding: 0.75rem;
  }
    .sm\:col-12 {
    width: 100%;
    padding: 0.75rem;
  }
    .sm\:form-col-6 {
    width: 50%;
    padding-left: 0.25rem;
    padding-right: 0.25rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
    .sm\:form-col-12 {
    width: 100%;
    padding-left: 0.25rem;
    padding-right: 0.25rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
}
@media (min-width: 768px) {
    .md\:col-4 {
    width: 33.333333%;
    padding: 0.75rem;
  }
    .md\:col-5 {
    width: 40%;
    padding: 0.75rem;
  }
    .md\:col-6 {
    width: 50%;
    padding: 0.75rem;
  }
    .md\:col-7 {
    width: 60%;
    padding: 0.75rem;
  }
    .md\:col-8 {
    width: 66.666667%;
    padding: 0.75rem;
  }
    .md\:form-col-12 {
    width: 100%;
    padding-left: 0.25rem;
    padding-right: 0.25rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
}
@media (min-width: 1024px) {
    .lg\:col-3 {
    width: 25%;
    padding: 0.75rem;
  }
    .lg\:col-6 {
    width: 50%;
    padding: 0.75rem;
  }
    .lg\:col-9 {
    width: 75%;
    padding: 0.75rem;
  }
}
@media (min-width: 1280px) {
    .xl\:col-3 {
    width: 25%;
    padding: 0.75rem;
  }
    .xl\:col-4 {
    width: 33.333333%;
    padding: 0.75rem;
  }
    .xl\:col-6 {
    width: 50%;
    padding: 0.75rem;
  }
    .xl\:col-9 {
    width: 75%;
    padding: 0.75rem;
  }
}
.first-letter\:uppercase::first-letter {
  text-transform: uppercase;
}
.first-letter\:capitalize::first-letter {
  text-transform: capitalize;
}
.placeholder\:font-public::-moz-placeholder {
  font-family: 'Public Sans', sans-serif;
}
.placeholder\:font-public::placeholder {
  font-family: 'Public Sans', sans-serif;
}
.placeholder\:font-rubik::-moz-placeholder {
  font-family: 'Rubik', sans-serif;
}
.placeholder\:font-rubik::placeholder {
  font-family: 'Rubik', sans-serif;
}
.placeholder\:text-\[10px\]::-moz-placeholder {
  font-size: 10px;
}
.placeholder\:text-\[10px\]::placeholder {
  font-size: 10px;
}
.placeholder\:text-lg::-moz-placeholder {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.placeholder\:text-lg::placeholder {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.placeholder\:text-xs::-moz-placeholder {
  font-size: 0.75rem;
  line-height: 1rem;
}
.placeholder\:text-xs::placeholder {
  font-size: 0.75rem;
  line-height: 1rem;
}
.placeholder\:font-normal::-moz-placeholder {
  font-weight: 400;
}
.placeholder\:font-normal::placeholder {
  font-weight: 400;
}
.placeholder\:text-\[\#6E7191\]::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(110 113 145 / var(--tw-text-opacity, 1));
}
.placeholder\:text-\[\#6E7191\]::placeholder {
  --tw-text-opacity: 1;
  color: rgb(110 113 145 / var(--tw-text-opacity, 1));
}
.placeholder\:text-\[\#A0A3BD\]::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(160 163 189 / var(--tw-text-opacity, 1));
}
.placeholder\:text-\[\#A0A3BD\]::placeholder {
  --tw-text-opacity: 1;
  color: rgb(160 163 189 / var(--tw-text-opacity, 1));
}
.placeholder\:text-paragraph::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(110 113 145 / var(--tw-text-opacity, 1));
}
.placeholder\:text-paragraph::placeholder {
  --tw-text-opacity: 1;
  color: rgb(110 113 145 / var(--tw-text-opacity, 1));
}
.before\:absolute::before {
  content: var(--tw-content);
  position: absolute;
}
.before\:inset-0::before {
  content: var(--tw-content);
  inset: 0px;
}
.before\:-top-2::before {
  content: var(--tw-content);
  top: -0.5rem;
}
.before\:left-0::before {
  content: var(--tw-content);
  left: 0px;
}
.before\:left-1\/2::before {
  content: var(--tw-content);
  left: 50%;
}
.before\:top-0::before {
  content: var(--tw-content);
  top: 0px;
}
.before\:top-1\/2::before {
  content: var(--tw-content);
  top: 50%;
}
.before\:-z-10::before {
  content: var(--tw-content);
  z-index: -10;
}
.before\:h-1::before {
  content: var(--tw-content);
  height: 0.25rem;
}
.before\:h-24::before {
  content: var(--tw-content);
  height: 6rem;
}
.before\:h-5::before {
  content: var(--tw-content);
  height: 1.25rem;
}
.before\:w-24::before {
  content: var(--tw-content);
  width: 6rem;
}
.before\:w-5::before {
  content: var(--tw-content);
  width: 1.25rem;
}
.before\:w-full::before {
  content: var(--tw-content);
  width: 100%;
}
.before\:-translate-x-1\/2::before {
  content: var(--tw-content);
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.before\:-translate-y-1\/2::before {
  content: var(--tw-content);
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.before\:scale-\[1\.03\]::before {
  content: var(--tw-content);
  --tw-scale-x: 1.03;
  --tw-scale-y: 1.03;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.before\:rounded-full::before {
  content: var(--tw-content);
  border-radius: 9999px;
}
.before\:border-\[3px\]::before {
  content: var(--tw-content);
  border-width: 3px;
}
.before\:border-primary::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 0 107 / var(--tw-border-opacity, 1));
}
.before\:bg-primary::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 107 / var(--tw-bg-opacity, 1));
}
.before\:bg-white::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.after\:absolute::after {
  content: var(--tw-content);
  position: absolute;
}
.after\:top-3::after {
  content: var(--tw-content);
  top: 0.75rem;
}
.after\:hidden::after {
  content: var(--tw-content);
  display: none;
}
.after\:h-2::after {
  content: var(--tw-content);
  height: 0.5rem;
}
.after\:w-2::after {
  content: var(--tw-content);
  width: 0.5rem;
}
.after\:rounded-full::after {
  content: var(--tw-content);
  border-radius: 9999px;
}
.after\:bg-\[\#FFDB1F\]::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 219 31 / var(--tw-bg-opacity, 1));
}
.after\:shadow::after {
  content: var(--tw-content);
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.last\:mb-0:last-child {
  margin-bottom: 0px;
}
.last\:border-b-0:last-child {
  border-bottom-width: 0px;
}
.last\:border-none:last-child {
  border-style: none;
}
.last\:pb-0:last-child {
  padding-bottom: 0px;
}
.last\:pr-3:last-child {
  padding-right: 0.75rem;
}
.focus-within\:border-primary:focus-within {
  --tw-border-opacity: 1;
  border-color: rgb(255 0 107 / var(--tw-border-opacity, 1));
}
.focus-within\:border-primary\/20:focus-within {
  border-color: rgb(255 0 107 / 0.2);
}
.focus-within\:bg-white:focus-within {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.hover\:border-primary:hover {
  --tw-border-opacity: 1;
  border-color: rgb(255 0 107 / var(--tw-border-opacity, 1));
}
.hover\:border-primary\/30:hover {
  border-color: rgb(255 0 107 / 0.3);
}
.hover\:bg-\[\#00749B\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(0 116 155 / var(--tw-bg-opacity, 1));
}
.hover\:bg-\[\#F7F7FC\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(247 247 252 / var(--tw-bg-opacity, 1));
}
.hover\:bg-\[\#FFEDF4\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 244 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.hover\:bg-primary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 0 107 / var(--tw-bg-opacity, 1));
}
.hover\:bg-primary\/5:hover {
  background-color: rgb(255 0 107 / 0.05);
}
.hover\:bg-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.hover\:text-gray-400:hover {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.hover\:text-gray-500:hover {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.hover\:text-primary:hover {
  --tw-text-opacity: 1;
  color: rgb(255 0 107 / var(--tw-text-opacity, 1));
}
.hover\:text-red-500:hover {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.hover\:underline:hover {
  text-decoration-line: underline;
}
.hover\:shadow-filter:hover {
  --tw-shadow: 0px 8px 16px rgba(23, 31, 70, 0.08);
  --tw-shadow-colored: 0px 8px 16px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-xs:hover {
  --tw-shadow: 0px 6px 32px 0px rgba(0, 0, 0, 0.04);
  --tw-shadow-colored: 0px 6px 32px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.focus\:z-10:focus {
  z-index: 10;
}
.focus\:border-blue-300:focus {
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}
.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus\:ring:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.active\:bg-gray-100:active {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.active\:text-gray-500:active {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.active\:text-gray-700:active {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.group:focus-within .group-focus-within\:visible {
  visibility: visible;
}
.group:hover .group-hover\:fill-white {
  fill: #fff;
}
.group:hover .group-hover\:text-primary {
  --tw-text-opacity: 1;
  color: rgb(255 0 107 / var(--tw-text-opacity, 1));
}
@media (min-width: 640px) {

  .sm\:absolute {
    position: absolute;
  }

  .sm\:bottom-16 {
    bottom: 4rem;
  }

  .sm\:left-8 {
    left: 2rem;
  }

  .sm\:top-12 {
    top: 3rem;
  }

  .sm\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .sm\:mb-0 {
    margin-bottom: 0px;
  }

  .sm\:mt-8 {
    margin-top: 2rem;
  }

  .sm\:inline-block {
    display: inline-block;
  }

  .sm\:inline {
    display: inline;
  }

  .sm\:flex {
    display: flex;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:h-\[calc\(100vh_-_290px\)\] {
    height: calc(100vh - 290px);
  }

  .sm\:h-auto {
    height: auto;
  }

  .sm\:w-1\/2 {
    width: 50%;
  }

  .sm\:w-36 {
    width: 9rem;
  }

  .sm\:w-48 {
    width: 12rem;
  }

  .sm\:w-60 {
    width: 15rem;
  }

  .sm\:w-\[360px\] {
    width: 360px;
  }

  .sm\:w-fit {
    width: -moz-fit-content;
    width: fit-content;
  }

  .sm\:max-w-\[300px\] {
    max-width: 300px;
  }

  .sm\:max-w-xs {
    max-width: 20rem;
  }

  .sm\:flex-1 {
    flex: 1 1 0%;
  }

  .sm\:table-fixed {
    table-layout: fixed;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .sm\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .sm\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .sm\:grid-cols-\[repeat\(auto-fill\2c _minmax\(185px\2c _1fr\)\)\] {
    grid-template-columns: repeat(auto-fill, minmax(185px, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:items-start {
    align-items: flex-start;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:justify-start {
    justify-content: flex-start;
  }

  .sm\:justify-end {
    justify-content: flex-end;
  }

  .sm\:justify-center {
    justify-content: center;
  }

  .sm\:justify-between {
    justify-content: space-between;
  }

  .sm\:gap-6 {
    gap: 1.5rem;
  }

  .sm\:gap-\[18px\] {
    gap: 18px;
  }

  .sm\:rounded-xl {
    border-radius: 0.75rem;
  }

  .sm\:border-t-0 {
    border-top-width: 0px;
  }

  .sm\:bg-\[\#FF3B8E\] {
    --tw-bg-opacity: 1;
    background-color: rgb(255 59 142 / var(--tw-bg-opacity, 1));
  }

  .sm\:bg-white {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  }

  .sm\:p-10 {
    padding: 2.5rem;
  }

  .sm\:p-5 {
    padding: 1.25rem;
  }

  .sm\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .sm\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .sm\:pt-0 {
    padding-top: 0px;
  }

  .sm\:text-left {
    text-align: left;
  }

  .sm\:text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  }

  .sm\:shadow-xs {
    --tw-shadow: 0px 6px 32px 0px rgba(0, 0, 0, 0.04);
    --tw-shadow-colored: 0px 6px 32px 0px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
}
@media (min-width: 768px) {

  .md\:top-\[85px\] {
    top: 85px;
  }

  .md\:z-10 {
    z-index: 10;
  }

  .md\:mb-0 {
    margin-bottom: 0px;
  }

  .md\:mt-0 {
    margin-top: 0px;
  }

  .md\:mt-4 {
    margin-top: 1rem;
  }

  .md\:block {
    display: block;
  }

  .md\:grid {
    display: grid;
  }

  .md\:hidden {
    display: none;
  }

  .md\:h-\[calc\(100vh-117px\)\] {
    height: calc(100vh - 117px);
  }

  .md\:h-\[calc\(100vh-127px\)\] {
    height: calc(100vh - 127px);
  }

  .md\:h-\[calc\(100vh-85px\)\] {
    height: calc(100vh - 85px);
  }

  .md\:h-auto {
    height: auto;
  }

  .md\:w-80 {
    width: 20rem;
  }

  .md\:w-\[322px\] {
    width: 322px;
  }

  .md\:w-\[calc\(100\%-340px\)\] {
    width: calc(100% - 340px);
  }

  .md\:w-auto {
    width: auto;
  }

  .md\:w-fit {
    width: -moz-fit-content;
    width: fit-content;
  }

  .md\:grid-flow-row {
    grid-auto-flow: row;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:justify-center {
    justify-content: center;
  }

  .md\:gap-4 {
    gap: 1rem;
  }

  .md\:gap-6 {
    gap: 1.5rem;
  }

  .md\:overflow-auto {
    overflow: auto;
  }

  .md\:rounded-lg {
    border-radius: 0.5rem;
  }

  .md\:pl-0 {
    padding-left: 0px;
  }

  .md\:pl-8 {
    padding-left: 2rem;
  }
}
@media (min-width: 1024px) {

  .lg\:bottom-8 {
    bottom: 2rem;
  }

  .lg\:top-\[74px\] {
    top: 74px;
  }

  .lg\:mx-14 {
    margin-left: 3.5rem;
    margin-right: 3.5rem;
  }

  .lg\:mb-0 {
    margin-bottom: 0px;
  }

  .lg\:mt-0 {
    margin-top: 0px;
  }

  .lg\:block {
    display: block;
  }

  .lg\:flex {
    display: flex;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:h-\[calc\(100vh-220px\)\] {
    height: calc(100vh - 220px);
  }

  .lg\:\!w-full {
    width: 100% !important;
  }

  .lg\:w-52 {
    width: 13rem;
  }

  .lg\:w-\[305px\] {
    width: 305px;
  }

  .lg\:w-\[930px\] {
    width: 930px;
  }

  .lg\:w-\[calc\(100\%-320px\)\] {
    width: calc(100% - 320px);
  }

  .lg\:w-fit {
    width: -moz-fit-content;
    width: fit-content;
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:justify-start {
    justify-content: flex-start;
  }

  .lg\:gap-6 {
    gap: 1.5rem;
  }
}
@media (min-width: 1280px) {

  .xl\:w-\[360px\] {
    width: 360px;
  }

  .xl\:w-\[calc\(100\%-377px\)\] {
    width: calc(100% - 377px);
  }

  .xl\:\!max-w-\[305px\] {
    max-width: 305px !important;
  }

  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .xl\:flex-row {
    flex-direction: row;
  }

  .xl\:gap-8 {
    gap: 2rem;
  }
}
@media (min-width: 0px) and (max-width: 640px) {

  .xst\:fixed {
    position: fixed;
  }

  .xst\:absolute {
    position: absolute;
  }

  .xst\:bottom-12 {
    bottom: 3rem;
  }

  .xst\:bottom-5 {
    bottom: 1.25rem;
  }

  .xst\:left-1\/2 {
    left: 50%;
  }

  .xst\:z-10 {
    z-index: 10;
  }

  .xst\:w-full {
    width: 100%;
  }

  .xst\:-translate-x-1\/2 {
    --tw-translate-x: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .xst\:rounded-t-2xl {
    border-top-left-radius: 1rem;
    border-top-right-radius: 1rem;
  }

  .xst\:pb-16 {
    padding-bottom: 4rem;
  }
}
@media (min-width: 0px) and (max-width: 767px) {

  .xh\:fixed {
    position: fixed;
  }

  .xh\:left-0 {
    left: 0px;
  }

  .xh\:w-full {
    width: 100%;
  }

  .xh\:justify-between {
    justify-content: space-between;
  }

  .xh\:border-y {
    border-top-width: 1px;
    border-bottom-width: 1px;
  }

  .xh\:border-\[\#EFF0F6\] {
    --tw-border-opacity: 1;
    border-color: rgb(239 240 246 / var(--tw-border-opacity, 1));
  }

  .xh\:bg-white {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  }

  .xh\:p-4 {
    padding: 1rem;
  }
}
.ltr\:-left-3:where([dir="ltr"], [dir="ltr"] *) {
  left: -0.75rem;
}
.ltr\:right-0:where([dir="ltr"], [dir="ltr"] *) {
  right: 0px;
}
.ltr\:right-2\.5:where([dir="ltr"], [dir="ltr"] *) {
  right: 0.625rem;
}
.ltr\:right-3:where([dir="ltr"], [dir="ltr"] *) {
  right: 0.75rem;
}
.ltr\:right-5:where([dir="ltr"], [dir="ltr"] *) {
  right: 1.25rem;
}
.ltr\:-ml-8:where([dir="ltr"], [dir="ltr"] *) {
  margin-left: -2rem;
}
.ltr\:rounded-l-lg:where([dir="ltr"], [dir="ltr"] *) {
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.ltr\:rounded-r-lg:where([dir="ltr"], [dir="ltr"] *) {
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}
.ltr\:rounded-br-lg:where([dir="ltr"], [dir="ltr"] *) {
  border-bottom-right-radius: 0.5rem;
}
.ltr\:rounded-tr-lg:where([dir="ltr"], [dir="ltr"] *) {
  border-top-right-radius: 0.5rem;
}
.ltr\:pl-2:where([dir="ltr"], [dir="ltr"] *) {
  padding-left: 0.5rem;
}
.ltr\:text-left:where([dir="ltr"], [dir="ltr"] *) {
  text-align: left;
}
.ltr\:after\:right-2\.5:where([dir="ltr"], [dir="ltr"] *)::after {
  content: var(--tw-content);
  right: 0.625rem;
}
@media (min-width: 640px) {

  .ltr\:sm\:border-r:where([dir="ltr"], [dir="ltr"] *) {
    border-right-width: 1px;
  }
}
@media (min-width: 768px) {

  .ltr\:md\:right-5:where([dir="ltr"], [dir="ltr"] *) {
    right: 1.25rem;
  }
}
.rtl\:-right-3:where([dir="rtl"], [dir="rtl"] *) {
  right: -0.75rem;
}
.rtl\:left-0:where([dir="rtl"], [dir="rtl"] *) {
  left: 0px;
}
.rtl\:left-2\.5:where([dir="rtl"], [dir="rtl"] *) {
  left: 0.625rem;
}
.rtl\:left-3:where([dir="rtl"], [dir="rtl"] *) {
  left: 0.75rem;
}
.rtl\:left-5:where([dir="rtl"], [dir="rtl"] *) {
  left: 1.25rem;
}
.rtl\:-mr-8:where([dir="rtl"], [dir="rtl"] *) {
  margin-right: -2rem;
}
.rtl\:-rotate-90:where([dir="rtl"], [dir="rtl"] *) {
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rtl\:rotate-180:where([dir="rtl"], [dir="rtl"] *) {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rtl\:rounded-l-lg:where([dir="rtl"], [dir="rtl"] *) {
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.rtl\:rounded-r-lg:where([dir="rtl"], [dir="rtl"] *) {
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}
.rtl\:rounded-bl-lg:where([dir="rtl"], [dir="rtl"] *) {
  border-bottom-left-radius: 0.5rem;
}
.rtl\:rounded-tl-lg:where([dir="rtl"], [dir="rtl"] *) {
  border-top-left-radius: 0.5rem;
}
.rtl\:pr-2:where([dir="rtl"], [dir="rtl"] *) {
  padding-right: 0.5rem;
}
.rtl\:text-right:where([dir="rtl"], [dir="rtl"] *) {
  text-align: right;
}
.rtl\:after\:left-2\.5:where([dir="rtl"], [dir="rtl"] *)::after {
  content: var(--tw-content);
  left: 0.625rem;
}
@media (min-width: 640px) {

  .rtl\:sm\:border-l:where([dir="rtl"], [dir="rtl"] *) {
    border-left-width: 1px;
  }
}
@media (min-width: 768px) {

  .rtl\:md\:left-5:where([dir="rtl"], [dir="rtl"] *) {
    left: 1.25rem;
  }
}
.\[\&_li\]\:mb-6 li {
  margin-bottom: 1.5rem;
}
.\[\&_li\]\:text-\[40px\] li {
  font-size: 40px;
}
.\[\&_li\]\:font-semibold li {
  font-weight: 600;
}
.\[\&_li\]\:leading-10 li {
  line-height: 2.5rem;
}


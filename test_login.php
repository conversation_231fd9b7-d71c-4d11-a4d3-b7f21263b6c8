<?php

// Test login API
$url = 'http://127.0.0.1:8000/api/auth/login';
$data = [
    'email' => '<EMAIL>',
    'password' => '123456'
];

$headers = [
    'Content-Type: application/json',
    'x-api-key: 121212'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: " . $httpCode . "\n";
echo "Response: " . $response . "\n";

if ($httpCode === 201) {
    echo "✅ Login successful!\n";
} else {
    echo "❌ Login failed!\n";
}
